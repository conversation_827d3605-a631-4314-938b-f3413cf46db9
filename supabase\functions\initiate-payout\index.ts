import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { userId, amount } = await req.json();

    if (!userId || !amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return new Response(JSON.stringify({ error: 'User ID and a valid amount are required.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Create a Supabase client with the service role key for secure backend operations
    const supabaseAdminClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Fetch guard's bank details from the database
    const { data: bankDetails, error: bankDetailsError } = await supabaseAdminClient
      .from('guard_bank_details')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (bankDetailsError || !bankDetails) {
      console.error('Error fetching bank details:', bankDetailsError?.message || 'Bank details not found.');
      return new Response(JSON.stringify({ error: 'Guard bank details not found. Please ensure they are entered in the profile.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    // Fetch guard's profile for name
    const { data: profile, error: profileError } = await supabaseAdminClient
      .from('profiles')
      .select('first_name, last_name')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      console.error('Error fetching profile:', profileError?.message || 'Profile not found.');
      return new Response(JSON.stringify({ error: 'Guard profile not found.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    const cashfreePayoutsClientId = Deno.env.get('CASHFREE_PAYOUTS_CLIENT_ID');
    const cashfreePayoutsClientSecret = Deno.env.get('CASHFREE_PAYOUTS_CLIENT_SECRET');

    if (!cashfreePayoutsClientId || !cashfreePayoutsClientSecret) {
      return new Response(JSON.stringify({ error: 'Cashfree Payouts API keys not configured in Supabase secrets.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    // Generate a unique transfer ID
    const transferId = `MIRAZSEC_PAYOUT_${userId}_${Date.now()}`;

    // Cashfree Payouts API endpoint (use sandbox for testing)
    const cashfreePayoutsApiUrl = 'https://api.cashfree.com/payout/v1/authorize'; // Or sandbox: 'https://sandbox.cashfree.com/payout/v1/authorize'

    const payoutResponse = await fetch(cashfreePayoutsApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Id': cashfreePayoutsClientId,
        'X-Client-Secret': cashfreePayoutsClientSecret,
      },
      body: JSON.stringify({
        fund_account: {
          account_number: bankDetails.account_number,
          ifsc: bankDetails.ifsc_code,
          bank_account_holder_name: bankDetails.account_holder_name,
        },
        transfer_id: transferId,
        amount: parseFloat(amount),
        transfer_mode: 'IMPS', // Or NEFT, RTGS, UPI
        remarks: `Salary payout for ${profile.first_name} ${profile.last_name}`,
      }),
    });

    const payoutData = await payoutResponse.json();

    if (!payoutResponse.ok) {
      console.error('Cashfree Payouts API error:', payoutData);
      // Record failed payment
      await supabaseAdminClient.from('salary_payments').insert({
        user_id: userId,
        amount: parseFloat(amount),
        status: 'Failed',
        cashfree_payout_id: payoutData.utr || payoutData.referenceId || null,
      });
      return new Response(JSON.stringify({ error: payoutData.message || 'Failed to initiate payout with Cashfree' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: payoutResponse.status,
      });
    }

    // Record successful payment initiation
    const { error: insertPaymentError } = await supabaseAdminClient.from('salary_payments').insert({
      user_id: userId,
      amount: parseFloat(amount),
      status: 'Processing', // Status will be updated by webhook later
      cashfree_payout_id: payoutData.utr || payoutData.referenceId || null,
    });

    if (insertPaymentError) {
      console.error('Error recording payment in DB:', insertPaymentError.message);
      // This is a critical error, but the payout might have gone through.
      // Log and notify admin.
    }

    return new Response(JSON.stringify({ message: 'Payout initiated successfully!', payoutData }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error in initiate-payout function:', error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});