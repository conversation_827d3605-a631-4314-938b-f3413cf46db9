import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { CalendarDays } from "lucide-react";
import React, { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { format } from "date-fns";

interface Shift {
  id: string;
  user_id: string;
  shift_date: string;
  start_time: string;
  end_time: string;
  location: string;
  created_at: string;
}

const GuardShiftSchedule = () => {
  const { user, loading: authLoading } = useAuth();
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [loadingShifts, setLoadingShifts] = useState(true);

  useEffect(() => {
    const fetchShifts = async () => {
      if (!user) {
        setLoadingShifts(false);
        return;
      }

      setLoadingShifts(true);
      const { data, error } = await supabase
        .from("shifts")
        .select("*")
        .eq("user_id", user.id)
        .order("shift_date", { ascending: true })
        .order("start_time", { ascending: true });

      if (error) {
        toast.error("Failed to load shifts: " + error.message);
        console.error("Error fetching shifts:", error);
      } else {
        setShifts(data as Shift[]);
      }
      setLoadingShifts(false);
    };

    if (!authLoading) {
      fetchShifts();
    }
  }, [user, authLoading]);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Shift Schedule</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <CalendarDays className="inline-block mr-2 h-5 w-5 text-green-500" />
            Upcoming Shifts
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loadingShifts ? (
            <p className="text-sm text-muted-foreground">Loading shifts...</p>
          ) : shifts.length > 0 ? (
            <div className="grid gap-3">
              {shifts.map((shift) => (
                <div key={shift.id} className="border rounded-md p-4">
                  <h3 className="font-medium">{format(new Date(shift.shift_date), "PPP")}</h3>
                  <p className="text-sm text-muted-foreground">{`${shift.start_time} - ${shift.end_time}`}</p>
                  <p className="text-sm mt-1">Location: {shift.location}</p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No upcoming shifts scheduled.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GuardShiftSchedule;