import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CheckCircle, XCircle, Star, TrendingUp, Globe, Smartphone, IndianRupee, Code, Cloud, DollarSign } from "lucide-react";
import { Button } from "@/components/ui/button"; // Import Button component
import { Link } from "react-router-dom"; // Import Link for navigation

const WhyChooseUs = () => {
  const competitors = [
    {
      name: "Trackforce Valiant (TrackTik India arm)",
      type: "Global company with operations in India",
      offer: "Workforce management, guard tour, compliance, scheduling, reporting",
      strengths: "International brand, large enterprise focus, mobile app",
      weakness: "Expensive for mid-size Indian agencies, complex setup",
    },
    {
      name: "TimeLabs",
      type: "Indian company",
      offer: "Security guard management, biometric attendance, shift scheduling",
      strengths: "Integration with biometric devices, payroll",
      weakness: "UI/UX is outdated; lacks advanced AI/panic alert tools",
    },
    {
      name: "Guards<PERSON> (Used internationally, some Indian clients)",
      type: "International company",
      offer: "Patrol management, real-time updates, reports, guard tracking",
      strengths: "Sleek design, mobile-ready",
      weakness: "No India-specific customization, pricey USD pricing",
    },
    {
      name: "SmartGuard App by SmartGroup India",
      type: "Indian company",
      offer: "Guard monitoring, duty allocation, real-time check-in/out",
      strengths: "Indian deployment, security-focused",
      weakness: "Limited analytics, lesser automation, minimal AI use",
    },
    {
      name: "Zuper",
      type: "Global company",
      offer: "Field workforce automation, guard tracking, ticketing",
      strengths: "Powerful integrations, modern SaaS",
      weakness: "Not exclusively for security firms; priced for mid-to-large teams",
    },
  ];

  const mirazSecAdvantages = [
    { icon: <IndianRupee className="h-5 w-5 text-green-500" />, title: "India-first Design", description: "Local language, regional needs, GST invoices, UPI-ready" },
    { icon: <Star className="h-5 w-5 text-yellow-500" />, title: "AI-Powered Features", description: "Incident prediction, risk modeling, AI alerts" },
    { icon: <Smartphone className="h-5 w-5 text-blue-500" />, title: "Field-Friendly UX", description: "Built mobile-first for guards in remote locations" },
    { icon: <DollarSign className="h-5 w-5 text-green-500" />, title: "Affordable Pricing", description: "₹1L/year vs ₹2–5L/year charged by global players" },
    { icon: <Code className="h-5 w-5 text-purple-500" />, title: "Fully Customizable", description: "White-label + source code option available" },
    { icon: <Cloud className="h-5 w-5 text-indigo-500" />, title: "Hybrid Model", description: "SaaS + Self-hosted = wider market reach" },
  ];

  return (
    <div className="space-y-8 p-4 md:p-8">
      <h1 className="text-4xl font-bold text-center mb-8">
        Why Choose MirazSec?
      </h1>
      <p className="text-lg text-center text-muted-foreground max-w-3xl mx-auto">
        Discover how MirazSec stands out from the competition by offering tailored solutions for the Indian security market.
      </p>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-center flex items-center justify-center">
            <TrendingUp className="mr-2 h-6 w-6 text-orange-500" />
            Top Indian Competitors
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {competitors.map((comp, index) => (
              <Card key={index} className="p-4 flex flex-col h-full">
                <CardHeader className="pb-2">
                  <CardTitle className="text-xl font-semibold">{comp.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">{comp.type}</p>
                </CardHeader>
                <CardContent className="flex-1 space-y-2 text-sm">
                  <p><strong>Offers:</strong> {comp.offer}</p>
                  <p><strong>Strengths:</strong> {comp.strengths}</p>
                  <p><strong>Weakness:</strong> {comp.weakness}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-center flex items-center justify-center">
            <Star className="mr-2 h-6 w-6 text-yellow-500" />
            MirazSec's Competitive Advantages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {mirazSecAdvantages.map((advantage, index) => (
              <Card key={index} className="p-4 flex flex-col items-center text-center h-full">
                <div className="mb-3">{advantage.icon}</div>
                <CardTitle className="text-xl font-semibold mb-2">{advantage.title}</CardTitle>
                <p className="text-sm text-muted-foreground">{advantage.description}</p>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card className="w-full mt-8 p-6 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800 text-center">
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-blue-700 dark:text-blue-300">
            Ready to Experience the MirazSec Difference?
          </CardTitle>
        </CardHeader>
        <CardContent className="text-lg text-gray-700 dark:text-gray-300">
          <p className="mb-4">
            Join the growing number of security agencies choosing MirazSec for a smarter, more efficient, and locally-attuned security operation.
          </p>
          <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg">
            <Link to="/signup">Get Started Today</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default WhyChooseUs;