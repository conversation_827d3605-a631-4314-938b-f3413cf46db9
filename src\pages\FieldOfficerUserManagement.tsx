import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Users, Edit } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useOrganization } from "@/hooks/useOrganization"; // Import useOrganization

interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
  email: string | null; // Assuming email can be fetched or joined from auth.users
}

const FieldOfficerUserManagement = () => {
  const { organization, plan, loading: orgLoading, canAddGuard, canAddClient } = useOrganization(); // Use the hook
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentEditingUser, setCurrentEditingUser] = useState<UserProfile | null>(null);
  const [editRole, setEditRole] = useState<string>("");

  const fetchUsers = async () => {
    setLoading(true);
    // Fetch profiles and join with auth.users to get email
    const { data, error } = await supabase
      .from("profiles")
      .select(`
          id,
          first_name,
          last_name,
          role,
          auth_users:auth.users(email)
        `);

    if (error) {
      toast.error("Failed to load users: " + error.message);
      console.error("Error fetching users:", error);
    } else {
      // Map the data to include email from the joined auth.users table
      const usersWithEmails: UserProfile[] = data.map((profile: any) => ({
        id: profile.id,
        first_name: profile.first_name,
        last_name: profile.last_name,
        role: profile.role,
        email: profile.auth_users?.email || null, // Access email from the joined table
      }));
      setUsers(usersWithEmails);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchUsers();

    // Set up real-time subscription for profiles
    const channel = supabase
      .channel('profiles_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'profiles' },
        (payload) => {
          console.log('Profile change received!', payload);
          fetchUsers(); // Re-fetch data on any change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const handleEditClick = (user: UserProfile) => {
    setCurrentEditingUser(user);
    setEditRole(user.role || "");
    setIsEditDialogOpen(true);
  };

  const handleUpdateUserRole = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentEditingUser) return;

    // Check limits before allowing role change to 'guard' or 'client'
    if (editRole === 'guard' && !canAddGuard && currentEditingUser.role !== 'guard') {
      toast.error(`Upgrade to a higher plan to add more than ${plan?.limits.guards} guards.`);
      setLoading(false); // Ensure loading is reset
      return;
    }
    if (editRole === 'client' && !canAddClient && currentEditingUser.role !== 'client') {
      toast.error(`Upgrade to a higher plan to add more than ${plan?.limits.clients} clients.`);
      setLoading(false); // Ensure loading is reset
      return;
    }

    setLoading(true);
    const { error } = await supabase
      .from("profiles")
      .update({ role: editRole })
      .eq("id", currentEditingUser.id);

    if (error) {
      toast.error("Failed to update user role: " + error.message);
      console.error("User role update error:", error);
    } else {
      toast.success("User role updated successfully!");
      setIsEditDialogOpen(false);
      fetchUsers(); // Re-fetch users to show updated data
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">User Management</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <Users className="inline-block mr-2 h-5 w-5 text-purple-500" />
            System Users
          </CardTitle>
        </CardHeader>
        <CardContent>
          {orgLoading || loading ? (
            <p className="text-sm text-muted-foreground">Loading users...</p>
          ) : (
            <>
              {organization && plan && (
                <div className="mb-4 text-sm text-muted-foreground">
                  <p>Current Plan: <span className="font-semibold capitalize">{plan.name}</span></p>
                  <p>Guards: {organization.current_guards_count} / {plan.limits.guards === 'unlimited' ? 'Unlimited' : plan.limits.guards}</p>
                  <p>Clients: {organization.current_clients_count} / {plan.limits.clients === 'unlimited' ? 'Unlimited' : plan.limits.clients}</p>
                </div>
              )}
              {users.length === 0 ? (
                <p className="text-sm text-muted-foreground">No users found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {user.first_name || "N/A"} {user.last_name || ""}
                          </TableCell>
                          <TableCell>{user.email || "N/A"}</TableCell>
                          <TableCell className="capitalize">{user.role || "N/A"}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => handleEditClick(user)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Edit User Role Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User Role</DialogTitle>
            <DialogDescription>
              Update the role for {currentEditingUser?.first_name} {currentEditingUser?.last_name} ({currentEditingUser?.email}).
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateUserRole} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="editRole">Role</Label>
              <Select onValueChange={setEditRole} value={editRole}>
                <SelectTrigger id="editRole">
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="guard" disabled={!canAddGuard && currentEditingUser?.role !== 'guard'}>Guard {(!canAddGuard && currentEditingUser?.role !== 'guard') && `(Limit: ${plan?.limits.guards})`}</SelectItem>
                  <SelectItem value="client" disabled={!canAddClient && currentEditingUser?.role !== 'client'}>Client {(!canAddClient && currentEditingUser?.role !== 'client') && `(Limit: ${plan?.limits.clients})`}</SelectItem>
                  <SelectItem value="field_officer">Field Officer</SelectItem>
                  <SelectItem value="company_admin">Company Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FieldOfficerUserManagement;