import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { MadeWithDyad } from "./made-with-dyad"; // Assuming this is a general component

interface ProtectedRouteProps {
  requiredRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ requiredRoles }) => {
  const { session, loading, profile, error, connectionStatus } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-xl">
            {connectionStatus === 'connecting' ? 'Connecting to authentication service...' : 'Loading authentication...'}
          </p>
          {connectionStatus === 'connecting' && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              This may take a few moments...
            </p>
          )}
        </div>
        <div className="absolute bottom-4">
          <MadeWithDyad />
        </div>
      </div>
    );
  }

  if (error || connectionStatus === 'error') {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
        <div className="text-center space-y-4 max-w-md mx-auto p-6">
          <div className="text-red-500 text-6xl">⚠️</div>
          <h1 className="text-2xl font-bold text-red-600 dark:text-red-400">Authentication Error</h1>
          <p className="text-gray-700 dark:text-gray-300">
            {error || 'Failed to connect to authentication service'}
          </p>
          <div className="space-y-2">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
            >
              Retry
            </button>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              If the problem persists, please contact support.
            </p>
          </div>
        </div>
        <div className="absolute bottom-4">
          <MadeWithDyad />
        </div>
      </div>
    );
  }

  if (!session) {
    // User is not authenticated, redirect to login page
    return <Navigate to="/login" replace />;
  }

  // If roles are required, check if the user's role is among them
  if (requiredRoles && profile?.role && !requiredRoles.includes(profile.role)) {
    // User is authenticated but does not have the required role
    return <Navigate to="/unauthorized" replace />;
  }

  // User is authenticated and has the required role (if any)
  return <Outlet />;
};

export default ProtectedRoute;