import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { MadeWithDyad } from "./made-with-dyad"; // Assuming this is a general component

interface ProtectedRouteProps {
  requiredRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ requiredRoles }) => {
  const { session, loading, profile } = useAuth();

  if (loading) {
    // You can render a loading spinner or skeleton here
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
        <p className="text-xl">Loading authentication...</p>
        <div className="absolute bottom-4">
          <MadeWithDyad />
        </div>
      </div>
    );
  }

  if (!session) {
    // User is not authenticated, redirect to login page
    return <Navigate to="/login" replace />;
  }

  // If roles are required, check if the user's role is among them
  if (requiredRoles && profile?.role && !requiredRoles.includes(profile.role)) {
    // User is authenticated but does not have the required role
    return <Navigate to="/unauthorized" replace />;
  }

  // User is authenticated and has the required role (if any)
  return <Outlet />;
};

export default ProtectedRoute;