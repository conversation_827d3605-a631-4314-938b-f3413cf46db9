import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { BellRing } from "lucide-react";
import { format } from "date-fns";

interface PanicAlert {
  id: string;
  user_id: string;
  status: string;
  created_at: string;
  latitude: number | null;
  longitude: number | null;
}

const GuardPanicAlertsOverview = () => {
  const { user, loading: authLoading } = useAuth();
  const [alerts, setAlerts] = useState<PanicAlert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPanicAlerts = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      setLoading(true);
      const { data, error } = await supabase
        .from("panic_alerts")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        toast.error("Failed to load your panic alerts: " + error.message);
        console.error("Error fetching guard panic alerts:", error);
      } else {
        setAlerts(data as PanicAlert[]);
      }
      setLoading(false);
    };

    if (!authLoading) {
      fetchPanicAlerts();
    }

    // Set up real-time subscription for panic alerts
    const channel = supabase
      .channel('guard_panic_alerts_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'panic_alerts' },
        (payload) => {
          console.log('Panic alert change received for guard overview!', payload);
          if (payload.new.user_id === user?.id || payload.old.user_id === user?.id) {
            fetchPanicAlerts(); // Re-fetch data if the change is relevant to the current user
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, authLoading]);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Panic Alerts</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <BellRing className="inline-block mr-2 h-5 w-5 text-red-500" />
            My Submitted Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading alerts...</p>
          ) : alerts.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have not submitted any panic alerts yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Alert Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Location</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {alerts.map((alert) => (
                    <TableRow key={alert.id}>
                      <TableCell className="font-medium">
                        {format(new Date(alert.created_at), "PPpp")}
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          alert.status === 'Active' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        }`}>
                          {alert.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        {alert.latitude && alert.longitude ? (
                          <a
                            href={`https://www.google.com/maps/search/?api=1&query=${alert.latitude},${alert.longitude}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline flex items-center"
                          >
                            View Map
                          </a>
                        ) : (
                          "Location not available"
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GuardPanicAlertsOverview;