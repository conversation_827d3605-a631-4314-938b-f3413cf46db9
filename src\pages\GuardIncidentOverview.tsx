import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { AlertTriangle, MapPin, Edit, MessageSquare } from "lucide-react"; // Added MessageSquare icon
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea"; // Import Textarea

interface Incident {
  id: string;
  user_id: string;
  title: string;
  description: string;
  location: string;
  image_url: string | null;
  created_at: string;
  latitude: number | null;
  longitude: number | null;
  status: string;
  incident_comments: IncidentComment[]; // Added comments to the interface
}

interface IncidentComment {
  id: string;
  incident_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

const GuardIncidentOverview = () => {
  const { user, loading: authLoading } = useAuth();
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentIncident, setCurrentIncident] = useState<Incident | null>(null);
  const [editStatus, setEditStatus] = useState("");
  const [updateLoading, setUpdateLoading] = useState(false);
  const [newCommentText, setNewCommentText] = useState(""); // New state for new comment

  const fetchIncidents = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    setLoading(true);
    const { data, error } = await supabase
      .from("incidents")
      .select(`
          *,
          incident_comments (
            id,
            user_id,
            comment_text,
            created_at,
            profiles (
              first_name,
              last_name,
              role
            )
          )
        `)
      .eq("user_id", user.id)
      .order("created_at", { ascending: false });

    if (error) {
      toast.error("Failed to load your incidents: " + error.message);
      console.error("Error fetching guard incidents:", error);
      setIncidents([]);
    } else {
      // Sort comments within each incident by created_at
      const incidentsWithSortedComments = (data as Incident[]).map(incident => ({
        ...incident,
        incident_comments: incident.incident_comments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      }));
      setIncidents(incidentsWithSortedComments);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!authLoading) {
      fetchIncidents();
    }

    // Set up real-time subscription for incidents and comments
    const incidentsChannel = supabase
      .channel('guard_incident_overview_incidents_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incidents' },
        () => {
          fetchIncidents();
        }
      )
      .subscribe();

    const commentsChannel = supabase
      .channel('guard_incident_overview_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incident_comments' },
        () => {
          fetchIncidents(); // Re-fetch incidents to update comments
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(incidentsChannel);
      supabase.removeChannel(commentsChannel);
    };
  }, [user, authLoading]);

  const handleEditClick = (incident: Incident) => {
    setCurrentIncident(incident);
    setEditStatus(incident.status);
    setNewCommentText(""); // Clear new comment input
    setIsEditDialogOpen(true);
  };

  const handleUpdateStatus = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentIncident) return;

    setUpdateLoading(true);
    const { error } = await supabase
      .from("incidents")
      .update({ status: editStatus })
      .eq("id", currentIncident.id);

    if (error) {
      toast.error("Failed to update incident status: " + error.message);
      console.error("Incident status update error:", error);
    } else {
      toast.success("Incident status updated successfully!");
      setIsEditDialogOpen(false);
      fetchIncidents(); // Re-fetch incidents to show updated data
    }
    setUpdateLoading(false);
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentIncident || !newCommentText.trim()) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setUpdateLoading(true); // Use updateLoading for comment submission as well
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error("You must be logged in to add a comment.");
      setUpdateLoading(false);
      return;
    }

    const { error } = await supabase.from("incident_comments").insert({
      incident_id: currentIncident.id,
      user_id: user.id,
      comment_text: newCommentText.trim(),
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewCommentText("");
      fetchIncidents(); // Re-fetch incidents to show the new comment
    }
    setUpdateLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Incident Reports</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <AlertTriangle className="inline-block mr-2 h-5 w-5 text-red-500" />
            My Reported Incidents
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading incidents...</p>
          ) : incidents.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have not reported any incidents yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>GPS</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Image</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {incidents.map((incident) => (
                    <TableRow key={incident.id}>
                      <TableCell className="font-medium">
                        {format(new Date(incident.created_at), "PPpp")}
                      </TableCell>
                      <TableCell>{incident.title}</TableCell>
                      <TableCell>{incident.location}</TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          incident.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          incident.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          incident.status === 'Resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {incident.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        {incident.latitude && incident.longitude ? (
                          <a
                            href={`https://www.google.com/maps/search/?api=1&query=${incident.latitude},${incident.longitude}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline flex items-center"
                          >
                            <MapPin className="h-4 w-4 mr-1" /> View Map
                          </a>
                        ) : (
                          "N/A"
                        )}
                      </TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {incident.description}
                      </TableCell>
                      <TableCell>
                        {incident.image_url ? (
                          <a href={incident.image_url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                            View Image
                          </a>
                        ) : (
                          "N/A"
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(incident)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Incident Status Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Update Incident Status</DialogTitle>
            <DialogDescription>
              Update the status for the incident: "{currentIncident?.title}".
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateStatus} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="incidentDescription">Description</Label>
              <p id="incidentDescription" className="text-sm text-muted-foreground">
                {currentIncident?.description || "No description provided."}
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editStatus">Status</Label>
              <Select onValueChange={setEditStatus} value={editStatus}>
                <SelectTrigger id="editStatus">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Open">Open</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Resolved">Resolved</SelectItem>
                  <SelectItem value="Closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={updateLoading}>
                {updateLoading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>

          <div className="mt-6 pt-4 border-t">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              Comments
            </h3>
            <div className="space-y-3 max-h-48 overflow-y-auto pr-2">
              {currentIncident?.incident_comments && currentIncident.incident_comments.length > 0 ? (
                currentIncident.incident_comments.map((comment) => (
                  <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                    <p className="text-sm font-medium">
                      {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                      <span className="text-xs text-muted-foreground ml-2">
                        ({comment.profiles?.role || "N/A"})
                      </span>
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(comment.created_at), "PPpp")}
                    </p>
                    <p className="text-sm mt-1">{comment.comment_text}</p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">No comments yet.</p>
              )}
            </div>
            <form onSubmit={handleAddComment} className="mt-4 flex gap-2">
              <Textarea
                placeholder="Add a new comment..."
                value={newCommentText}
                onChange={(e) => setNewCommentText(e.target.value)}
                rows={2}
                className="flex-1"
                disabled={updateLoading}
              />
              <Button type="submit" disabled={updateLoading}>
                {updateLoading ? "Adding..." : "Add Comment"}
              </Button>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GuardIncidentOverview;