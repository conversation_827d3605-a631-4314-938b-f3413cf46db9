import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { Layout } from "./components/layout/Layout";
import GuardDashboard from "./pages/GuardDashboard";
import ClientDashboard from "./pages/ClientDashboard";
import FieldOfficerDashboard from "./pages/FieldOfficerDashboard";
import GuardIncidentReport from "./pages/GuardIncidentReport";
import ClientIncidentsAndComms from "./pages/ClientIncidentsAndComms";
import FieldOfficerUserManagement from "./pages/FieldOfficerUserManagement";
import GuardShiftSchedule from "./pages/GuardShiftSchedule";
import FieldOfficerReports from "./pages/FieldOfficerReports";
import LoginPage from "./pages/LoginPage";
import SignupPage from "./pages/SignupPage";
import { AuthProvider } from "./context/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";
import UnauthorizedPage from "./pages/UnauthorizedPage";
import GuardTimesheetSubmission from "./pages/GuardTimesheetSubmission";
import FieldOfficerTimesheets from "./pages/FieldOfficerTimesheets";
import FieldOfficerIncidentOverview from "./pages/FieldOfficerIncidentOverview";
import FieldOfficerCreateCommunication from "./pages/FieldOfficerCreateCommunication";
import FieldOfficerShiftScheduling from "./pages/FieldOfficerShiftScheduling";
import FieldOfficerShiftScheduleOverview from "./pages/FieldOfficerShiftScheduleOverview";
import ProfilePage from "./pages/ProfilePage";
import FieldOfficerCommunicationsOverview from "./pages/FieldOfficerCommunicationsOverview";
import ForgotPasswordPage from "./pages/ForgotPasswordPage";
import GuardTimesheetOverview from "./pages/GuardTimesheetOverview";
import GuardIncidentOverview from "./pages/GuardIncidentOverview";
import ClientIncidentReport from "./pages/ClientIncidentReport";
import ClientSubmitInquiry from "./pages/ClientSubmitInquiry";
import FieldOfficerClientInquiries from "./pages/FieldOfficerClientInquiries";
import ClientInquiryOverview from "./pages/ClientInquiryOverview";
import GuardCommunicationsOverview from "./pages/GuardCommunicationsOverview";
import FieldOfficerPanicAlerts from "./pages/FieldOfficerPanicAlerts";
import FieldOfficerCreateTask from "./pages/FieldOfficerCreateTask";
import FieldOfficerTasksOverview from "./pages/FieldOfficerTasksOverview";
import GuardMyTasks from "./pages/GuardMyTasks";
import FieldOfficerGuardLocations from "./pages/FieldOfficerGuardLocations";
import GuardPanicAlertsOverview from "./pages/GuardPanicAlertsOverview";
import ClientGuardLocations from "./pages/ClientGuardLocations";
import ClientReports from "./pages/ClientReports";
import GuardIncidentInsights from "./pages/GuardIncidentInsights";
import GuardSupportChat from "./pages/GuardSupportChat";
import FieldOfficerSupportChatOverview from "./pages/FieldOfficerSupportChatOverview";
import FieldOfficerPredictiveRisk from "./pages/FieldOfficerPredictiveRisk";
import FieldOfficerScheduleCommunication from "./pages/FieldOfficerScheduleCommunication";
import AdminDashboard from "./pages/AdminDashboard";
import AdminCompanyReports from "./pages/AdminCompanyReports";
import AdminSystemSettings from "./pages/AdminSystemSettings";
import ClientInvoicesPage from "./pages/ClientInvoicesPage";
import FieldOfficerManageServices from "./pages/FieldOfficerManageServices";
import FieldOfficerCreateInvoice from "./pages/FieldOfficerCreateInvoice";
import FieldOfficerInitiatePayout from "./pages/FieldOfficerInitiatePayout";
import FieldOfficerSalaryPaymentsOverview from "./pages/FieldOfficerSalaryPaymentsOverview";
import PricingPage from "./pages/PricingPage";
import WhyChooseUs from "./pages/WhyChooseUs";
import { AuthDebug } from "./components/AuthDebug";
import TestConnection from "./pages/TestConnection";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AuthProvider>
          <AuthDebug />
          <Layout>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/signup" element={<SignupPage />} />
              <Route path="/forgot-password" element={<ForgotPasswordPage />} />
              <Route path="/unauthorized" element={<UnauthorizedPage />} />
              <Route path="/plan" element={<PricingPage />} />
              <Route path="/why-choose-us" element={<WhyChooseUs />} />
              <Route path="/test-connection" element={<TestConnection />} />

              {/* Protected Routes for all authenticated users */}
              <Route element={<ProtectedRoute />}>
                <Route path="/profile" element={<ProfilePage />} />
              </Route>

              {/* Protected Routes for specific roles */}
              <Route element={<ProtectedRoute requiredRoles={["guard"]} />}>
                <Route path="/guard" element={<GuardDashboard />} />
                <Route path="/guard/report-incident" element={<GuardIncidentReport />} />
                <Route path="/guard/shift-schedule" element={<GuardShiftSchedule />} />
                <Route path="/guard/submit-timesheet" element={<GuardTimesheetSubmission />} />
                <Route path="/guard/timesheets" element={<GuardTimesheetOverview />} />
                <Route path="/guard/incidents" element={<GuardIncidentOverview />} />
                <Route path="/guard/communications" element={<GuardCommunicationsOverview />} />
                <Route path="/guard/my-tasks" element={<GuardMyTasks />} />
                <Route path="/guard/panic-alerts" element={<GuardPanicAlertsOverview />} />
                <Route path="/guard/incident-insights" element={<GuardIncidentInsights />} />
                <Route path="/guard/support-chat" element={<GuardSupportChat />} />
              </Route>

              <Route element={<ProtectedRoute requiredRoles={["client"]} />}>
                <Route path="/client" element={<ClientDashboard />} />
                <Route path="/client/incidents-comms" element={<ClientIncidentsAndComms />} />
                <Route path="/client/report-incident" element={<ClientIncidentReport />} />
                <Route path="/client/submit-inquiry" element={<ClientSubmitInquiry />} />
                <Route path="/client/my-inquiries" element={<ClientInquiryOverview />} />
                <Route path="/client/guard-locations" element={<ClientGuardLocations />} />
                <Route path="/client/reports" element={<ClientReports />} />
                <Route path="/client/invoices" element={<ClientInvoicesPage />} />
              </Route>

              <Route element={<ProtectedRoute requiredRoles={["field_officer"]} />}>
                <Route path="/field-officer" element={<FieldOfficerDashboard />} />
                <Route path="/field-officer/user-management" element={<FieldOfficerUserManagement />} />
                <Route path="/field-officer/reports" element={<FieldOfficerReports />} />
                <Route path="/field-officer/timesheets" element={<FieldOfficerTimesheets />} />
                <Route path="/field-officer/incidents" element={<FieldOfficerIncidentOverview />} />
                <Route path="/field-officer/create-communication" element={<FieldOfficerCreateCommunication />} />
                <Route path="/field-officer/schedule-communication" element={<FieldOfficerScheduleCommunication />} />
                <Route path="/field-officer/shift-scheduling" element={<FieldOfficerShiftScheduling />} />
                <Route path="/field-officer/shift-schedule-overview" element={<FieldOfficerShiftScheduleOverview />} />
                <Route path="/field-officer/communications-overview" element={<FieldOfficerCommunicationsOverview />} />
                <Route path="/field-officer/client-inquiries" element={<FieldOfficerClientInquiries />} />
                <Route path="/field-officer/panic-alerts" element={<FieldOfficerPanicAlerts />} />
                <Route path="/field-officer/create-task" element={<FieldOfficerCreateTask />} />
                <Route path="/field-officer/tasks-overview" element={<FieldOfficerTasksOverview />} />
                <Route path="/field-officer/guard-locations" element={<FieldOfficerGuardLocations />} />
                <Route path="/field-officer/support-chat-overview" element={<FieldOfficerSupportChatOverview />} />
                <Route path="/field-officer/predictive-risk" element={<FieldOfficerPredictiveRisk />} />
                <Route path="/field-officer/manage-services" element={<FieldOfficerManageServices />} />
                <Route path="/field-officer/create-invoice" element={<FieldOfficerCreateInvoice />} />
                <Route path="/field-officer/initiate-payout" element={<FieldOfficerInitiatePayout />} />
                <Route path="/field-officer/salary-payments-overview" element={<FieldOfficerSalaryPaymentsOverview />} />
              </Route>

              {/* New Protected Route for Company Admin */}
              <Route element={<ProtectedRoute requiredRoles={["company_admin"]} />}>
                <Route path="/admin" element={<AdminDashboard />} />
                <Route path="/admin/company-reports" element={<AdminCompanyReports />} />
                <Route path="/admin/system-settings" element={<AdminSystemSettings />} />
              </Route>

              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Layout>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;