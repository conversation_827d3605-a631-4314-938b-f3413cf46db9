import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Menu, Home, Shield, Users, MapPin, AlertCircle, MessageSquareText, UserCog, CalendarDays, BarChart2, LogOut, Clock, MessageSquarePlus, CalendarPlus, CalendarCheck, User as UserIcon, FileText, MessageCircleQuestion, ListChecks, BellRing, ListTodo, Brain, MessageCircle, TrendingUp, Settings, ReceiptText, Package, DollarSign, Banknote } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export function MobileSidebar() {
  const { session, loading, profile } = useAuth();

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      toast.error("Logout failed: " + error.message);
    } else {
      toast.success("Logged out successfully!");
    }
  };

  if (loading) {
    return (
      <Button variant="ghost" size="icon" className="md:hidden">
        <Menu className="h-6 w-6" />
      </Button>
    );
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-6 w-6" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0 w-64">
        <div className="space-y-4 py-4">
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
              MirazSec
            </h2>
            <div className="space-y-1">
              {session ? (
                <>
                  {/* Profile link for all authenticated users */}
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link to="/profile">
                      <Avatar className="mr-2 h-6 w-6">
                        <AvatarImage src={profile?.avatar_url || ""} alt={`${profile?.first_name} ${profile?.last_name}'s avatar`} />
                        <AvatarFallback>
                          {profile?.first_name ? profile.first_name.charAt(0) : ''}
                          {profile?.last_name ? profile.last_name.charAt(0) : ''}
                        </AvatarFallback>
                      </Avatar>
                      My Profile
                    </Link>
                  </Button>

                  {profile?.role === "company_admin" && (
                    <>
                      <h3 className="mt-4 mb-2 px-4 text-sm font-semibold tracking-tight">
                        Admin Tools
                      </h3>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/admin">
                          <Settings className="mr-2 h-4 w-4" />
                          Admin Dashboard
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/admin/company-reports">
                          <BarChart2 className="mr-2 h-4 w-4" />
                          Company Reports
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/admin/system-settings">
                          <Settings className="mr-2 h-4 w-4" />
                          System Settings
                        </Link>
                      </Button>
                    </>
                  )}

                  {profile?.role === "guard" && (
                    <>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard">
                          <Home className="mr-2 h-4 w-4" />
                          Guard Dashboard
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/report-incident">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          Report Incident
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/shift-schedule">
                          <CalendarDays className="mr-2 h-4 w-4" />
                          My Schedule
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/submit-timesheet">
                          <Clock className="mr-2 h-4 w-4" />
                          Submit Timesheet
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/timesheets">
                          <FileText className="mr-2 h-4 w-4" />
                          My Timesheets
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/incidents">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          My Incidents
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/communications">
                          <MessageSquareText className="mr-2 h-4 w-4" />
                          My Communications
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/my-tasks">
                          <ListTodo className="mr-2 h-4 w-4" />
                          My Tasks
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/panic-alerts">
                          <BellRing className="mr-2 h-4 w-4" />
                          My Panic Alerts
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/incident-insights">
                          <Brain className="mr-2 h-4 w-4" />
                          Incident Insights
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/guard/support-chat">
                          <MessageCircle className="mr-2 h-4 w-4" />
                          Support Chat
                        </Link>
                      </Button>
                    </>
                  )}
                  {profile?.role === "client" && (
                    <>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client">
                          <Users className="mr-2 h-4 w-4" />
                          Client Dashboard
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client/report-incident">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          Report Incident
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client/submit-inquiry">
                          <MessageCircleQuestion className="mr-2 h-4 w-4" />
                          Submit Inquiry
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client/my-inquiries">
                          <ListChecks className="mr-2 h-4 w-4" />
                          My Inquiries
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client/incidents-comms">
                          <MessageSquareText className="mr-2 h-4 w-4" />
                          Incidents & Comms
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client/guard-locations">
                          <MapPin className="mr-2 h-4 w-4" />
                          Guard Locations
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client/reports">
                          <BarChart2 className="mr-2 h-4 w-4" />
                          Reports & Analytics
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/client/invoices">
                          <ReceiptText className="mr-2 h-4 w-4" />
                          My Invoices
                        </Link>
                      </Button>
                    </>
                  )}
                  {profile?.role === "field_officer" && (
                    <>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer">
                          <MapPin className="mr-2 h-4 w-4" />
                          Field Officer Dashboard
                        </Link>
                      </Button>
                      <h3 className="mt-4 mb-2 px-4 text-sm font-semibold tracking-tight">
                        Operations
                      </h3>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/shift-scheduling">
                          <CalendarPlus className="mr-2 h-4 w-4" />
                          Schedule Shift
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/shift-schedule-overview">
                          <CalendarCheck className="mr-2 h-4 w-4" />
                          Shift Overview
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/create-communication">
                          <MessageSquarePlus className="mr-2 h-4 w-4" />
                          Create Communication
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/schedule-communication">
                          <CalendarPlus className="mr-2 h-4 w-4" />
                          Schedule Communication
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/communications-overview">
                          <MessageSquareText className="mr-2 h-4 w-4" />
                          Communications Overview
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/incidents">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          Incident Overview
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/panic-alerts">
                          <BellRing className="mr-2 h-4 w-4" />
                          Panic Alerts
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/create-task">
                          <ListTodo className="mr-2 h-4 w-4" />
                          Create Task
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/tasks-overview">
                          <ListChecks className="mr-2 h-4 w-4" />
                          Tasks Overview
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/guard-locations">
                          <MapPin className="mr-2 h-4 w-4" />
                          Guard Locations
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/support-chat-overview">
                          <MessageCircle className="mr-2 h-4 w-4" />
                          Support Chats
                        </Link>
                      </Button>

                      <h3 className="mt-4 mb-2 px-4 text-sm font-semibold tracking-tight">
                        Management & Analytics
                      </h3>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/user-management">
                          <UserCog className="mr-2 h-4 w-4" />
                          User Management
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/timesheets">
                          <Clock className="mr-2 h-4 w-4" />
                          Timesheet Overview
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/client-inquiries">
                          <MessageCircleQuestion className="mr-2 h-4 w-4" />
                          Client Inquiries
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/reports">
                          <BarChart2 className="mr-2 h-4 w-4" />
                          Reports
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/predictive-risk">
                          <TrendingUp className="mr-2 h-4 w-4" />
                          Predictive Risk
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/manage-services">
                          <Package className="mr-2 h-4 w-4" />
                          Manage Services
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/create-invoice">
                          <DollarSign className="mr-2 h-4 w-4" />
                          Create Invoice
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/initiate-payout">
                          <Banknote className="mr-2 h-4 w-4" />
                          Initiate Payout
                        </Link>
                      </Button>
                      <Button variant="ghost" className="w-full justify-start" asChild>
                        <Link to="/field-officer/salary-payments-overview">
                          <Banknote className="mr-2 h-4 w-4" />
                          Salary Payments
                        </Link>
                      </Button>
                    </>
                  )}
                  {/* Logout button for authenticated users */}
                  <Button variant="ghost" className="w-full justify-start" onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </Button>
                </>
              ) : (
                <>
                  {/* Login and Sign Up buttons for unauthenticated users */}
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link to="/login">
                      <LogOut className="mr-2 h-4 w-4 rotate-180" />
                      Login
                    </Link>
                  </Button>
                  <Button variant="ghost" className="w-full justify-start" asChild>
                    <Link to="/signup">
                      <UserCog className="mr-2 h-4 w-4" />
                      Sign Up
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </div>
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
              General
            </h2>
            <div className="space-y-1">
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link to="/">
                  <Shield className="mr-2 h-4 w-4" />
                  Home
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}