import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { format } from 'date-fns';

// Fix for default marker icon issue with Webpack
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});

interface GuardLocation {
  id: string;
  user_id: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

interface GuardMapProps {
  locations: GuardLocation[];
}

const GuardMap: React.FC<GuardMapProps> = ({ locations }) => {
  const mapRef = useRef<L.Map | null>(null);

  useEffect(() => {
    if (mapRef.current && locations.length > 0) {
      // Calculate bounds to fit all markers
      const latLngs = locations.map(loc => [loc.latitude, loc.longitude] as L.LatLngTuple);
      const bounds = L.latLngBounds(latLngs);
      mapRef.current.fitBounds(bounds, { padding: [50, 50] });
    }
  }, [locations]);

  // Set a default center if no locations are present, e.g., a central point or a known area
  const defaultCenter: L.LatLngTuple = [0, 0]; // Default to [0,0] if no locations
  const initialZoom = locations.length > 0 ? 13 : 2; // Zoom in if locations exist, otherwise world view

  return (
    <MapContainer
      center={locations.length > 0 ? [locations[0].latitude, locations[0].longitude] : defaultCenter}
      zoom={initialZoom}
      scrollWheelZoom={true}
      style={{ height: '500px', width: '100%', borderRadius: '0.5rem' }}
      whenCreated={mapInstance => { mapRef.current = mapInstance; }}
    >
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      {locations.map((location) => (
        <Marker key={location.id} position={[location.latitude, location.longitude]}>
          <Popup>
            <div className="font-semibold">
              {location.profiles?.first_name || "Unknown"} {location.profiles?.last_name || ""}
            </div>
            <div className="text-sm text-muted-foreground">
              Last Updated: {format(new Date(location.timestamp), "PPpp")}
            </div>
            <a
              href={`https://www.google.com/maps/search/?api=1&query=${location.latitude},${location.longitude}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline text-sm mt-1 block"
            >
              View on Google Maps
            </a>
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
};

export default GuardMap;