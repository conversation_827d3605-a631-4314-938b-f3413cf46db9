import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { useNavigate } from "react-router-dom";
import { MessageSquarePlus } from "lucide-react";

const ClientSubmitInquiry = () => {
  const { user } = useAuth();
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!user) {
      toast.error("You must be logged in to submit an inquiry.");
      setLoading(false);
      return;
    }

    if (!subject || !message) {
      toast.error("Please fill in both the subject and message fields.");
      setLoading(false);
      return;
    }

    const { error } = await supabase.from("client_inquiries").insert({
      client_id: user.id,
      subject,
      message,
      status: "Open", // Default status
    });

    if (error) {
      toast.error("Failed to submit inquiry: " + error.message);
      console.error("Inquiry submission error:", error);
    } else {
      toast.success("Inquiry submitted successfully!");
      setSubject("");
      setMessage("");
      navigate("/client/incidents-comms"); // Redirect to a relevant page, e.g., incidents/comms overview
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Submit New Inquiry</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquarePlus className="mr-2 h-5 w-5 text-blue-500" />
            Send a General Inquiry
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                placeholder="e.g., Question about billing, Security concern"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                placeholder="Write your detailed inquiry here..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                required
                rows={6}
              />
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Submitting..." : "Submit Inquiry"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientSubmitInquiry;