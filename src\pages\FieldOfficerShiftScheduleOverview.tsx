import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { CalendarCheck, Edit, Trash, CalendarIcon, Clock, User } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Shift {
  id: string;
  user_id: string;
  shift_date: string;
  start_time: string;
  end_time: string;
  location: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

interface GuardProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
}

const FieldOfficerShiftScheduleOverview = () => {
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [guards, setGuards] = useState<GuardProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentShift, setCurrentShift] = useState<Shift | null>(null);

  // State for edit form
  const [editGuardId, setEditGuardId] = useState<string>("");
  const [editShiftDate, setEditShiftDate] = useState<Date | undefined>(new Date());
  const [editStartTime, setEditStartTime] = useState("");
  const [editEndTime, setEditEndTime] = useState("");
  const [editLocation, setEditLocation] = useState("");

  const fetchShifts = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from("shifts")
      .select(`
        *,
        profiles (
          first_name,
          last_name
        )
      `)
      .order("shift_date", { ascending: false })
      .order("start_time", { ascending: false });

    if (error) {
      toast.error("Failed to load shifts: " + error.message);
      console.error("Error fetching shifts:", error);
    } else {
      setShifts(data as Shift[]);
    }
    setLoading(false);
  };

  const fetchGuards = async () => {
    const { data, error } = await supabase
      .from("profiles")
      .select("id, first_name, last_name")
      .eq("role", "guard");

    if (error) {
      toast.error("Failed to load guards for editing: " + error.message);
      console.error("Error fetching guards:", error);
    } else {
      setGuards(data || []);
    }
  };

  useEffect(() => {
    fetchShifts();
    fetchGuards();

    // Set up real-time subscription for shifts
    const channel = supabase
      .channel('shifts_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'shifts' },
        (payload) => {
          console.log('Shift change received!', payload);
          fetchShifts(); // Re-fetch data on any change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const handleEditClick = (shift: Shift) => {
    setCurrentShift(shift);
    setEditGuardId(shift.user_id);
    setEditShiftDate(new Date(shift.shift_date));
    setEditStartTime(shift.start_time);
    setEditEndTime(shift.end_time);
    setEditLocation(shift.location);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (shift: Shift) => {
    setCurrentShift(shift);
    setIsDeleteDialogOpen(true);
  };

  const handleUpdateShift = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentShift) return;

    if (!editGuardId || !editShiftDate || !editStartTime || !editEndTime || !editLocation) {
      toast.error("Please fill in all required fields for the shift.");
      return;
    }

    setLoading(true);
    const { error } = await supabase
      .from("shifts")
      .update({
        user_id: editGuardId,
        shift_date: format(editShiftDate, "yyyy-MM-dd"),
        start_time: editStartTime,
        end_time: editEndTime,
        location: editLocation,
      })
      .eq("id", currentShift.id);

    if (error) {
      toast.error("Failed to update shift: " + error.message);
      console.error("Shift update error:", error);
    } else {
      toast.success("Shift updated successfully!");
      setIsEditDialogOpen(false);
      fetchShifts(); // Re-fetch shifts to show updated data
    }
    setLoading(false);
  };

  const confirmDeleteShift = async () => {
    if (!currentShift) return;

    setLoading(true);
    const { error } = await supabase
      .from("shifts")
      .delete()
      .eq("id", currentShift.id);

    if (error) {
      toast.error("Failed to delete shift: " + error.message);
      console.error("Shift deletion error:", error);
    } else {
      toast.success("Shift deleted successfully!");
      setIsDeleteDialogOpen(false);
      fetchShifts(); // Re-fetch shifts to show updated data
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Shift Schedule Overview</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <CalendarCheck className="inline-block mr-2 h-5 w-5 text-indigo-500" />
            All Scheduled Shifts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading shifts...</p>
          ) : shifts.length === 0 ? (
            <p className="text-sm text-muted-foreground">No shifts scheduled yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Guard</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Scheduled On</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {shifts.map((shift) => (
                    <TableRow key={shift.id}>
                      <TableCell className="font-medium">
                        {format(new Date(shift.shift_date), "PPP")}
                      </TableCell>
                      <TableCell>{`${shift.start_time} - ${shift.end_time}`}</TableCell>
                      <TableCell>
                        {shift.profiles?.first_name || "N/A"} {shift.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell>{shift.location}</TableCell>
                      <TableCell>
                        {format(new Date(shift.created_at), "PPpp")}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(shift)} className="mr-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(shift)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Shift Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Shift</DialogTitle>
            <DialogDescription>
              Make changes to the scheduled shift here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateShift} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="editGuard">Assign Guard</Label>
              <Select onValueChange={setEditGuardId} value={editGuardId}>
                <SelectTrigger id="editGuard">
                  <SelectValue placeholder="Select a guard" />
                </SelectTrigger>
                <SelectContent>
                  {guards.map((guard) => (
                    <SelectItem key={guard.id} value={guard.id}>
                      {guard.first_name} {guard.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editShiftDate">Shift Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !editShiftDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {editShiftDate ? format(editShiftDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={editShiftDate}
                    onSelect={setEditShiftDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="editStartTime">Start Time</Label>
                <Input
                  id="editStartTime"
                  type="time"
                  required
                  value={editStartTime}
                  onChange={(e) => setEditStartTime(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="editEndTime">End Time</Label>
                <Input
                  id="editEndTime"
                  type="time"
                  required
                  value={editEndTime}
                  onChange={(e) => setEditEndTime(e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editLocation">Location</Label>
              <Input
                id="editLocation"
                placeholder="e.g., Main Gate, Sector C"
                required
                value={editLocation}
                onChange={(e) => setEditLocation(e.target.value)}
              />
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Shift Alert Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the shift scheduled for{" "}
              <span className="font-semibold">
                {currentShift ? format(new Date(currentShift.shift_date), "PPP") : ""}
              </span>{" "}
              from{" "}
              <span className="font-semibold">
                {currentShift ? currentShift.start_time : ""}
              </span>{" "}
              to{" "}
              <span className="font-semibold">
                {currentShift ? currentShift.end_time : ""}
              </span>{" "}
              at{" "}
              <span className="font-semibold">
                {currentShift ? currentShift.location : ""}
              </span>.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteShift} disabled={loading}>
              {loading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default FieldOfficerShiftScheduleOverview;