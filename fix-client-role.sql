-- SQL script to fix client role in Supabase database
-- Run this in your Supabase SQL Editor

-- First, check current profiles
SELECT id, email, role, first_name, last_name FROM auth.users 
LEFT JOIN public.profiles ON auth.users.id = public.profiles.id;

-- Update the client user role (replace '<EMAIL>' with actual client email)
UPDATE public.profiles 
SET role = 'client' 
WHERE id = (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

-- Verify the update
SELECT p.id, u.email, p.role, p.first_name, p.last_name 
FROM public.profiles p
JOIN auth.users u ON p.id = u.id
WHERE u.email = '<EMAIL>';
