import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react-swc';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom', // Simulate a browser environment
    globals: true, // Make test utilities like 'expect' globally available
    setupFiles: './src/setupTests.ts', // Setup file for @testing-library/jest-dom
    css: false, // Disable CSS processing for tests
    coverage: {
      provider: 'v8', // Or 'istanbul'
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/main.tsx', // Entry point, usually not tested directly
        'src/App.tsx', // Main App component, often covered by E2E or integration tests
        'src/vite-env.d.ts',
        'src/components/ui/**', // Exclude shadcn/ui components as they are third-party
        'src/hooks/use-toast.ts', // Complex hook, might need dedicated testing
        'src/utils/toast.ts', // Utility for toasts, often tested via integration
        'src/integrations/supabase/client.ts', // Supabase client setup
        'supabase/functions/**', // Edge functions are tested separately
      ],
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});