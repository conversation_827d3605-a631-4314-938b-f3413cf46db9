import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { ListTodo, Edit, Trash, CalendarIcon, ArrowUp, ArrowDown } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";

interface Task {
  id: string;
  title: string;
  description: string | null;
  user_id: string; // Assigned guard's ID
  status: string;
  priority: string;
  due_date: string | null;
  created_by: string; // Field officer's ID
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null; // Assigned guard's profile
  creator_profile: {
    first_name: string | null;
    last_name: string | null;
  } | null; // Creator's profile
}

interface GuardProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
}

const FieldOfficerTasksOverview = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [guards, setGuards] = useState<GuardProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState<Task | null>(null);

  // State for edit form
  const [editTitle, setEditTitle] = useState("");
  const [editDescription, setEditDescription] = useState("");
  const [editAssignedGuardId, setEditAssignedGuardId] = useState<string>("");
  const [editStatus, setEditStatus] = useState("");
  const [editPriority, setEditPriority] = useState("");
  const [editDueDate, setEditDueDate] = useState<Date | undefined>(undefined);

  // State for filters and sorting
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterPriority, setFilterPriority] = useState<string>("all");
  const [filterAssignedGuard, setFilterAssignedGuard] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const fetchTasks = async () => {
    setLoading(true);
    let query = supabase
      .from("tasks")
      .select(`
          *,
          profiles!tasks_user_id_fkey (
            first_name,
            last_name
          ),
          creator_profile:profiles!tasks_created_by_fkey (
            first_name,
            last_name
          )
        `);

    // Apply filters
    if (filterStatus !== "all") {
      query = query.eq("status", filterStatus);
    }
    if (filterPriority !== "all") {
      query = query.eq("priority", filterPriority);
    }
    if (filterAssignedGuard !== "all") {
      query = query.eq("user_id", filterAssignedGuard);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === "asc" });

    const { data, error } = await query;

    if (error) {
      toast.error("Failed to load tasks: " + error.message);
      console.error("Error fetching tasks:", error);
    } else {
      setTasks(data as Task[]);
    }
    setLoading(false);
  };

  const fetchGuards = async () => {
    const { data, error } = await supabase
      .from("profiles")
      .select("id, first_name, last_name")
      .eq("role", "guard");

    if (error) {
      toast.error("Failed to load guards for editing: " + error.message);
      console.error("Error fetching guards:", error);
    } else {
      setGuards(data || []);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [filterStatus, filterPriority, filterAssignedGuard, sortBy, sortOrder]); // Re-fetch on filter/sort change

  useEffect(() => {
    fetchGuards();
  }, []);

  const handleEditClick = (task: Task) => {
    setCurrentTask(task);
    setEditTitle(task.title);
    setEditDescription(task.description || "");
    setEditAssignedGuardId(task.user_id);
    setEditStatus(task.status);
    setEditPriority(task.priority);
    setEditDueDate(task.due_date ? new Date(task.due_date) : undefined);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (task: Task) => {
    setCurrentTask(task);
    setIsDeleteDialogOpen(true);
  };

  const handleUpdateTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentTask) return;

    if (!editTitle || !editAssignedGuardId || !editStatus || !editPriority) {
      toast.error("Please fill in all required fields for the task.");
      return;
    }

    setLoading(true);
    const { error } = await supabase
      .from("tasks")
      .update({
        title: editTitle,
        description: editDescription || null,
        user_id: editAssignedGuardId,
        status: editStatus,
        priority: editPriority,
        due_date: editDueDate ? format(editDueDate, "yyyy-MM-dd") : null,
      })
      .eq("id", currentTask.id);

    if (error) {
      toast.error("Failed to update task: " + error.message);
      console.error("Task update error:", error);
    } else {
      toast.success("Task updated successfully!");
      setIsEditDialogOpen(false);
      fetchTasks(); // Re-fetch tasks to show updated data
    }
    setLoading(false);
  };

  const confirmDeleteTask = async () => {
    if (!currentTask) return;

    setLoading(true);
    const { error } = await supabase
      .from("tasks")
      .delete()
      .eq("id", currentTask.id);

    if (error) {
      toast.error("Failed to delete task: " + error.message);
      console.error("Task deletion error:", error);
    } else {
      toast.success("Task deleted successfully!");
      setIsDeleteDialogOpen(false);
      fetchTasks(); // Re-fetch tasks to show updated data
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Task Management</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <ListTodo className="inline-block mr-2 h-5 w-5 text-green-500" />
            All Assigned Tasks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="filterStatus">Filter by Status</Label>
              <Select onValueChange={setFilterStatus} value={filterStatus}>
                <SelectTrigger id="filterStatus">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="filterPriority">Filter by Priority</Label>
              <Select onValueChange={setFilterPriority} value={filterPriority}>
                <SelectTrigger id="filterPriority">
                  <SelectValue placeholder="All Priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="filterAssignedGuard">Filter by Guard</Label>
              <Select onValueChange={setFilterAssignedGuard} value={filterAssignedGuard}>
                <SelectTrigger id="filterAssignedGuard">
                  <SelectValue placeholder="All Guards" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Guards</SelectItem>
                  {guards.map((guard) => (
                    <SelectItem key={guard.id} value={guard.id}>
                      {guard.first_name} {guard.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="sortBy">Sort By</Label>
              <Select onValueChange={setSortBy} value={sortBy}>
                <SelectTrigger id="sortBy">
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Created At</SelectItem>
                  <SelectItem value="due_date">Due Date</SelectItem>
                  <SelectItem value="priority">Priority</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[100px]">
              <Label htmlFor="sortOrder">Order</Label>
              <Select onValueChange={setSortOrder} value={sortOrder}>
                <SelectTrigger id="sortOrder">
                  <SelectValue placeholder="Order" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {loading ? (
            <p className="text-sm text-muted-foreground">Loading tasks...</p>
          ) : tasks.length === 0 ? (
            <p className="text-sm text-muted-foreground">No tasks assigned yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Assigned To</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Created By</TableHead>
                    <TableHead>Created At</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell className="font-medium">{task.title}</TableCell>
                      <TableCell>
                        {task.profiles?.first_name || "N/A"} {task.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          task.status === 'Pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                          task.status === 'In Progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          task.status === 'Completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {task.status}
                        </span>
                      </TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          task.priority === 'Low' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          task.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          task.priority === 'High' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {task.priority}
                        </span>
                      </TableCell>
                      <TableCell>{task.due_date ? format(new Date(task.due_date), "PPP") : "N/A"}</TableCell>
                      <TableCell>
                        {task.creator_profile?.first_name || "N/A"} {task.creator_profile?.last_name || ""}
                      </TableCell>
                      <TableCell>{format(new Date(task.created_at), "PPpp")}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(task)} className="mr-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(task)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Task Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
            <DialogDescription>
              Make changes to the task here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateTask} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="editTitle">Task Title</Label>
              <Input
                id="editTitle"
                placeholder="e.g., Patrol Sector A"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editDescription">Description (Optional)</Label>
              <Textarea
                id="editDescription"
                placeholder="Provide detailed instructions..."
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                rows={4}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editAssignedGuard">Assign to Guard</Label>
              <Select onValueChange={setEditAssignedGuardId} value={editAssignedGuardId}>
                <SelectTrigger id="editAssignedGuard">
                  <SelectValue placeholder="Select a guard" />
                </SelectTrigger>
                <SelectContent>
                  {guards.map((guard) => (
                    <SelectItem key={guard.id} value={guard.id}>
                      {guard.first_name} {guard.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="editStatus">Status</Label>
                <Select onValueChange={setEditStatus} value={editStatus}>
                  <SelectTrigger id="editStatus">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Pending">Pending</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                    <SelectItem value="Cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="editPriority">Priority</Label>
                <Select onValueChange={setEditPriority} value={editPriority}>
                  <SelectTrigger id="editPriority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editDueDate">Due Date (Optional)</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !editDueDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {editDueDate ? format(editDueDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={editDueDate}
                    onSelect={setEditDueDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Task Alert Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the task titled{" "}
              <span className="font-semibold">
                "{currentTask ? currentTask.title : ""}"
              </span>{" "}
              assigned to{" "}
              <span className="font-semibold">
                {currentTask?.profiles?.first_name || "N/A"} {currentTask?.profiles?.last_name || ""}
              </span>.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteTask} disabled={loading}>
              {loading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default FieldOfficerTasksOverview;