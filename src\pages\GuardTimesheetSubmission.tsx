import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { CalendarIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";

const GuardTimesheetSubmission = () => {
  const { user } = useAuth();
  const [shiftDate, setShiftDate] = useState<Date | undefined>(new Date());
  const [hoursWorked, setHoursWorked] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) {
      toast.error("You must be logged in to submit a timesheet.");
      return;
    }
    if (!shiftDate || !hoursWorked) {
      toast.error("Please fill in all required fields (Date and Hours Worked).");
      return;
    }
    const parsedHours = parseFloat(hoursWorked);
    if (isNaN(parsedHours) || parsedHours <= 0) {
      toast.error("Please enter a valid number for hours worked.");
      return;
    }

    setLoading(true);
    const { error } = await supabase.from("timesheets").insert({
      user_id: user.id,
      shift_date: format(shiftDate, "yyyy-MM-dd"),
      hours_worked: parsedHours,
      description: description || null,
    });

    if (error) {
      toast.error("Failed to submit timesheet: " + error.message);
      console.error("Timesheet submission error:", error);
    } else {
      toast.success("Timesheet submitted successfully!");
      setShiftDate(new Date());
      setHoursWorked("");
      setDescription("");
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Submit Timesheet</h1>
      <Card>
        <CardHeader>
          <CardTitle>New Timesheet Entry</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="shiftDate">Shift Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !shiftDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {shiftDate ? format(shiftDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={shiftDate}
                    onSelect={setShiftDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="hoursWorked">Hours Worked</Label>
              <Input
                id="hoursWorked"
                type="number"
                step="0.01"
                placeholder="e.g., 8.5"
                required
                value={hoursWorked}
                onChange={(e) => setHoursWorked(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="e.g., Regular shift, covered for John Doe"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
              />
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Submitting..." : "Submit Timesheet"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default GuardTimesheetSubmission;