"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { Brain, AlertTriangle, Lightbulb } from "lucide-react";
import { format } from "date-fns";
import { useOrganization } from "@/hooks/useOrganization"; // Import useOrganization hook

interface Incident {
  id: string;
  title: string;
  description: string;
  location: string;
  created_at: string;
  status: string;
}

interface IncidentInsights {
  summary: string;
  keywords: string[];
  recommendations: string[];
  riskLevel: string;
  trends: string;
}

const GuardIncidentInsights = () => {
  const { user, loading: authLoading } = useAuth();
  const { plan, loading: orgLoading, canAccessAiInsights } = useOrganization(); // Use the hook
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [selectedIncidentId, setSelectedIncidentId] = useState<string>("");
  const [insights, setInsights] = useState<IncidentInsights | null>(null);
  const [loadingIncidents, setLoadingIncidents] = useState(true);
  const [loadingInsights, setLoadingInsights] = useState(false);

  useEffect(() => {
    const fetchIncidents = async () => {
      if (!user) {
        setLoadingIncidents(false);
        return;
      }
      setLoadingIncidents(true);
      const { data, error } = await supabase
        .from("incidents")
        .select("id, title, description, location, created_at, status")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        toast.error("Failed to load your incidents: " + error.message);
        console.error("Error fetching guard incidents for insights:", error);
        setIncidents([]);
      } else {
        setIncidents(data as Incident[]);
      }
      setLoadingIncidents(false);
    };

    if (!authLoading && !orgLoading) { // Ensure auth and org data are loaded
      fetchIncidents();
    }
  }, [user, authLoading, orgLoading]); // Depend on user, authLoading, and orgLoading

  const handleAnalyzeIncident = async () => {
    if (!selectedIncidentId) {
      toast.error("Please select an incident to analyze.");
      return;
    }

    setLoadingInsights(true);
    setInsights(null); // Clear previous insights

    try {
      const { data, error } = await supabase.functions.invoke('analyze-incident', {
        body: { incidentId: selectedIncidentId },
      });

      if (error) {
        toast.error("Failed to get insights: " + error.message);
        console.error("Error invoking analyze-incident function:", error);
      } else {
        setInsights(data.insights);
        toast.success("Incident analyzed successfully!");
      }
    } catch (error: any) {
      toast.error("An unexpected error occurred: " + error.message);
      console.error("Unexpected error during incident analysis:", error);
    } finally {
      setLoadingInsights(false);
    }
  };

  const selectedIncident = incidents.find(inc => inc.id === selectedIncidentId);

  if (authLoading || orgLoading) {
    return (
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">Incident Insights (AI)</h1>
        <Card>
          <CardContent>
            <p className="text-sm text-muted-foreground">Loading plan details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Incident Insights (AI)</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="mr-2 h-5 w-5 text-purple-500" />
            Analyze Incident
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!canAccessAiInsights ? (
            <div className="text-center p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md">
              <p className="font-semibold mb-2">AI Insights Not Available!</p>
              <p className="text-sm">
                Your current plan (<span className="capitalize">{plan?.name}</span>) does not include access to AI-powered incident insights.
              </p>
              <p className="text-sm mt-2">Upgrade your plan to unlock this feature.</p>
            </div>
          ) : (
            <>
              <div className="grid gap-2">
                <label htmlFor="selectIncident" className="text-sm font-medium">Select an Incident</label>
                <Select onValueChange={setSelectedIncidentId} value={selectedIncidentId} disabled={loadingIncidents}>
                  <SelectTrigger id="selectIncident">
                    <SelectValue placeholder={loadingIncidents ? "Loading incidents..." : "Select an incident"} />
                  </SelectTrigger>
                  <SelectContent>
                    {incidents.length === 0 ? (
                      <SelectItem value="no-incidents" disabled>No incidents available</SelectItem>
                    ) : (
                      incidents.map((incident) => (
                        <SelectItem key={incident.id} value={incident.id}>
                          {format(new Date(incident.created_at), "PPP")} - {incident.title}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              {selectedIncident && (
                <div className="border rounded-md p-3 text-sm bg-gray-50 dark:bg-gray-800">
                  <p className="font-semibold">{selectedIncident.title}</p>
                  <p className="text-muted-foreground truncate">{selectedIncident.description}</p>
                  <p className="text-muted-foreground">Location: {selectedIncident.location}</p>
                </div>
              )}
              <Button onClick={handleAnalyzeIncident} className="w-full" disabled={!selectedIncidentId || loadingInsights}>
                {loadingInsights ? "Analyzing..." : "Get AI Insights"}
              </Button>
            </>
          )}
        </CardContent>
      </Card>

      {insights && canAccessAiInsights && ( // Only show insights if accessible
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lightbulb className="mr-2 h-5 w-5 text-yellow-500" />
              AI Insights
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-1">Summary:</h3>
              <p className="text-sm text-muted-foreground">{insights.summary}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-1">Keywords:</h3>
              <p className="text-sm text-muted-foreground">{insights.keywords.join(", ")}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-1">Risk Level:</h3>
              <p className="text-sm text-muted-foreground">{insights.riskLevel}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-1">Trends:</h3>
              <p className="text-sm text-muted-foreground">{insights.trends}</p>
            </div>
            <div>
              <h3 className="font-semibold mb-1">Recommendations:</h3>
              <ul className="list-disc list-inside text-sm text-muted-foreground">
                {insights.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default GuardIncidentInsights;