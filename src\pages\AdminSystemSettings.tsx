"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Settings } from "lucide-react";

const AdminSystemSettings: React.FC = () => {
  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">System Settings</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="mr-2 h-5 w-5 text-gray-500" />
            Global Application Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            This page is where company administrators can configure global settings for the MirazSec application.
            Features such as user role permissions, default notification settings, data retention policies,
            and integration configurations would be managed here.
          </p>
          <p className="text-sm text-muted-foreground mt-4">
            **Coming Soon:** Detailed configuration options will be available in future updates.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminSystemSettings;