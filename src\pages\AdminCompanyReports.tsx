"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { BarChart2, Users, AlertTriangle, Clock, CalendarDays, ListChecks, MessageCircleQuestion } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import IncidentStatusChart from "@/components/IncidentStatusChart"; // Reusing for overall incident status

interface StatusData {
  name: string;
  value: number;
}

const AdminCompanyReports: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [totalUsers, setTotalUsers] = useState<number | null>(null);
  const [totalIncidents, setTotalIncidents] = useState<number | null>(null);
  const [totalShifts, setTotalShifts] = useState<number | null>(null);
  const [totalTimesheets, setTotalTimesheets] = useState<number | null>(null);
  const [incidentStatusData, setIncidentStatusData] = useState<StatusData[]>([]);
  const [totalTasks, setTotalTasks] = useState<number | null>(null);
  const [openInquiries, setOpenInquiries] = useState<number | null>(null);

  useEffect(() => {
    const fetchReportsData = async () => {
      setLoading(true);
      let hasError = false;

      // Fetch Total Users
      const { count: usersCount, error: usersError } = await supabase
        .from("profiles")
        .select("*", { count: "exact", head: true });
      if (usersError) {
        console.error("Error fetching total users:", usersError);
        setTotalUsers(0);
        hasError = true;
      } else {
        setTotalUsers(usersCount);
      }

      // Fetch Total Incidents and Status
      const { data: incidentsData, count: incidentsTotal, error: incidentsError } = await supabase
        .from("incidents")
        .select("status", { count: "exact" });
      if (incidentsError) {
        console.error("Error fetching total incidents:", incidentsError);
        setTotalIncidents(0);
        setIncidentStatusData([]);
        hasError = true;
      } else {
        setTotalIncidents(incidentsTotal);
        const statusCounts: { [key: string]: number } = {};
        incidentsData.forEach((incident: { status: string }) => {
          statusCounts[incident.status] = (statusCounts[incident.status] || 0) + 1;
        });
        const formattedData = Object.keys(statusCounts).map(status => ({
          name: status,
          value: statusCounts[status],
        }));
        setIncidentStatusData(formattedData);
      }

      // Fetch Total Shifts
      const { count: shiftsCount, error: shiftsError } = await supabase
        .from("shifts")
        .select("*", { count: "exact", head: true });
      if (shiftsError) {
        console.error("Error fetching total shifts:", shiftsError);
        setTotalShifts(0);
        hasError = true;
      } else {
        setTotalShifts(shiftsCount);
      }

      // Fetch Total Timesheets
      const { count: timesheetsCount, error: timesheetsError } = await supabase
        .from("timesheets")
        .select("*", { count: "exact", head: true });
      if (timesheetsError) {
        console.error("Error fetching total timesheets:", timesheetsError);
        setTotalTimesheets(0);
        hasError = true;
      } else {
        setTotalTimesheets(timesheetsCount);
      }

      // Fetch Total Tasks
      const { count: tasksCount, error: tasksError } = await supabase
        .from("tasks")
        .select("*", { count: "exact", head: true });
      if (tasksError) {
        console.error("Error fetching total tasks:", tasksError);
        setTotalTasks(0);
        hasError = true;
      } else {
        setTotalTasks(tasksCount);
      }

      // Fetch Open Inquiries
      const { count: inquiriesCount, error: inquiriesError } = await supabase
        .from("client_inquiries")
        .select("*", { count: "exact", head: true })
        .in("status", ["Open", "In Progress"]);
      if (inquiriesError) {
        console.error("Error fetching open inquiries:", inquiriesError);
        setOpenInquiries(0);
        hasError = true;
      } else {
        setOpenInquiries(inquiriesCount);
      }

      setLoading(false);
      if (hasError) {
        toast.error("Some report data could not be loaded.");
      }
    };

    fetchReportsData();

    // Set up real-time subscriptions for relevant tables
    const channels = [
      supabase.channel('admin_reports_profiles_changes').on('postgres_changes', { event: '*', schema: 'public', table: 'profiles' }, () => fetchReportsData()).subscribe(),
      supabase.channel('admin_reports_incidents_changes').on('postgres_changes', { event: '*', schema: 'public', table: 'incidents' }, () => fetchReportsData()).subscribe(),
      supabase.channel('admin_reports_shifts_changes').on('postgres_changes', { event: '*', schema: 'public', table: 'shifts' }, () => fetchReportsData()).subscribe(),
      supabase.channel('admin_reports_timesheets_changes').on('postgres_changes', { event: '*', schema: 'public', table: 'timesheets' }, () => fetchReportsData()).subscribe(),
      supabase.channel('admin_reports_tasks_changes').on('postgres_changes', { event: '*', schema: 'public', table: 'tasks' }, () => fetchReportsData()).subscribe(),
      supabase.channel('admin_reports_inquiries_changes').on('postgres_changes', { event: '*', schema: 'public', table: 'client_inquiries' }, () => fetchReportsData()).subscribe(),
    ];

    return () => {
      channels.forEach(channel => supabase.removeChannel(channel));
    };
  }, []);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Company Reports</h1>
      <p className="text-muted-foreground">
        Comprehensive reports and analytics across all company operations.
      </p>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? <div className="text-2xl font-bold">Loading...</div> : <div className="text-2xl font-bold">{totalUsers}</div>}
            <p className="text-xs text-muted-foreground">All registered accounts</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Incidents</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? <div className="text-2xl font-bold">Loading...</div> : <div className="text-2xl font-bold">{totalIncidents}</div>}
            <p className="text-xs text-muted-foreground">All security incidents reported</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Shifts</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? <div className="text-2xl font-bold">Loading...</div> : <div className="text-2xl font-bold">{totalShifts}</div>}
            <p className="text-xs text-muted-foreground">All shifts scheduled</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Timesheets</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? <div className="text-2xl font-bold">Loading...</div> : <div className="text-2xl font-bold">{totalTimesheets}</div>}
            <p className="text-xs text-muted-foreground">All timesheet entries</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
            <ListChecks className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? <div className="text-2xl font-bold">Loading...</div> : <div className="text-2xl font-bold">{totalTasks}</div>}
            <p className="text-xs text-muted-foreground">All tasks created</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Client Inquiries</CardTitle>
            <MessageCircleQuestion className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? <div className="text-2xl font-bold">Loading...</div> : <div className="text-2xl font-bold">{openInquiries}</div>}
            <p className="text-xs text-muted-foreground">Inquiries awaiting action</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
            Overall Incident Status Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading chart data...</p>
          ) : incidentStatusData.length > 0 ? (
            <IncidentStatusChart data={incidentStatusData} />
          ) : (
            <p className="text-sm text-muted-foreground">No incident data available for charting.</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center">
            <BarChart2 className="mr-2 h-5 w-5 text-orange-500" />
            Further Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            This section can be expanded to include more detailed reports such as:
          </p>
          <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
            <li>Guard performance metrics (e.g., average response time to alerts)</li>
            <li>Client satisfaction trends from inquiries</li>
            <li>Shift coverage analysis</li>
            <li>Resource allocation efficiency</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminCompanyReports;