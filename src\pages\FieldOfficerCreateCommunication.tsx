import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { MessageSquarePlus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; // Import Quill styles

const FieldOfficerCreateCommunication = () => {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState(""); // Content will now be HTML from Quill
  const [targetRoles, setTargetRoles] = useState<string[]>([]); // New state for target roles
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const availableRoles = [
    { value: "guard", label: "Guards" },
    { value: "client", label: "Clients" },
    { value: "field_officer", label: "Field Officers" },
  ];

  const handleRoleChange = (role: string) => {
    setTargetRoles(prev =>
      prev.includes(role) ? prev.filter(r => r !== role) : [...prev, role]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!title || !content || content === "<p><br></p>" || targetRoles.length === 0) { // Check for empty content and no roles
      toast.error("Please fill in both the title and content fields, and select at least one target role.");
      setLoading(false);
      return;
    }

    const { error } = await supabase.from("communications").insert({
      title,
      content,
      target_roles: targetRoles, // Include target roles in the insert
    });

    if (error) {
      toast.error("Failed to send communication: " + error.message);
      console.error("Communication submission error:", error);
    } else {
      toast.success("Communication sent successfully!");
      setTitle("");
      setContent(""); // Clear Quill content
      setTargetRoles([]); // Clear selected roles
      navigate("/field-officer/communications-overview"); // Redirect to a relevant page, e.g., incidents/comms overview
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Create New Communication</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquarePlus className="mr-2 h-5 w-5 text-blue-500" />
            Send a Message
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                placeholder="e.g., Important Update, Security Advisory"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="content">Content</Label>
              <ReactQuill
                theme="snow"
                value={content}
                onChange={setContent}
                placeholder="Write your message here..."
                className="min-h-[150px]" // Ensure enough height for the editor
              />
            </div>
            <div className="grid gap-2">
              <Label>Target Roles</Label>
              <div className="flex flex-wrap gap-2">
                {availableRoles.map((role) => (
                  <Button
                    key={role.value}
                    type="button"
                    variant={targetRoles.includes(role.value) ? "default" : "outline"}
                    onClick={() => handleRoleChange(role.value)}
                  >
                    {role.label}
                  </Button>
                ))}
              </div>
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Sending..." : "Send Communication"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerCreateCommunication;