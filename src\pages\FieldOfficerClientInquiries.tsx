import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { MessageCircleQuestion, Edit, MessageSquare } from "lucide-react"; // Added MessageSquare icon
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ClientInquiry {
  id: string;
  client_id: string;
  subject: string;
  message: string;
  status: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

interface InquiryComment {
  id: string;
  inquiry_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

const FieldOfficerClientInquiries = () => {
  const [inquiries, setInquiries] = useState<ClientInquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentInquiry, setCurrentInquiry] = useState<ClientInquiry | null>(null);
  const [inquiryComments, setInquiryComments] = useState<InquiryComment[]>([]);

  // State for edit form
  const [editStatus, setEditStatus] = useState("");
  const [newCommentText, setNewCommentText] = useState(""); // New state for new comment

  const fetchInquiries = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from("client_inquiries")
      .select(`
          *,
          profiles (
            first_name,
            last_name
          )
        `)
      .order("created_at", { ascending: false });

    if (error) {
      toast.error("Failed to load client inquiries: " + error.message);
      console.error("Error fetching client inquiries:", error);
    } else {
      setInquiries(data as ClientInquiry[]);
    }
    setLoading(false);
  };

  const fetchInquiryComments = async (inquiryId: string) => {
    const { data, error } = await supabase
      .from("client_inquiry_comments")
      .select(`
          *,
          profiles (
            first_name,
            last_name,
            role
          )
        `)
      .eq("inquiry_id", inquiryId)
      .order("created_at", { ascending: true });

    if (error) {
      console.error("Error fetching inquiry comments:", error.message);
      setInquiryComments([]);
    } else {
      setInquiryComments(data as InquiryComment[]);
    }
  };

  useEffect(() => {
    fetchInquiries();

    // Set up real-time subscription for client inquiries
    const inquiriesChannel = supabase
      .channel('client_inquiries_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'client_inquiries' },
        (payload) => {
          console.log('Client inquiry change received!', payload);
          fetchInquiries(); // Re-fetch data on any change
        }
      )
      .subscribe();

    // Set up real-time subscription for inquiry comments
    const commentsChannel = supabase
      .channel('client_inquiry_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'client_inquiry_comments' },
        (payload) => {
          console.log('Inquiry comment change received!', payload);
          if (currentInquiry && payload.new.inquiry_id === currentInquiry.id) {
            fetchInquiryComments(currentInquiry.id); // Re-fetch comments for the open dialog
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(inquiriesChannel);
      supabase.removeChannel(commentsChannel);
    };
  }, [currentInquiry]); // Re-run effect if currentInquiry changes to update comment subscription

  const handleEditClick = (inquiry: ClientInquiry) => {
    setCurrentInquiry(inquiry);
    setEditStatus(inquiry.status);
    setNewCommentText(""); // Clear new comment input
    fetchInquiryComments(inquiry.id); // Fetch comments when dialog opens
    setIsEditDialogOpen(true);
  };

  const handleUpdateInquiryStatus = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentInquiry) return;

    setLoading(true);
    const { error } = await supabase
      .from("client_inquiries")
      .update({ status: editStatus })
      .eq("id", currentInquiry.id);

    if (error) {
      toast.error("Failed to update inquiry status: " + error.message);
      console.error("Inquiry status update error:", error);
    } else {
      toast.success("Inquiry status updated successfully!");
      setIsEditDialogOpen(false);
      fetchInquiries(); // Re-fetch inquiries to show updated data
    }
    setLoading(false);
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentInquiry || !newCommentText.trim()) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setLoading(true); // Use general loading for comment submission
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error("You must be logged in to add a comment.");
      setLoading(false);
      return;
    }

    const { error } = await supabase.from("client_inquiry_comments").insert({
      inquiry_id: currentInquiry.id,
      user_id: user.id,
      comment_text: newCommentText.trim(),
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewCommentText("");
      fetchInquiryComments(currentInquiry.id); // Re-fetch comments to show the new one
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Client Inquiries</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageCircleQuestion className="inline-block mr-2 h-5 w-5 text-purple-500" />
            All Client Inquiries
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading inquiries...</p>
          ) : inquiries.length === 0 ? (
            <p className="text-sm text-muted-foreground">No client inquiries found.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inquiries.map((inquiry) => (
                    <TableRow key={inquiry.id}>
                      <TableCell className="font-medium">
                        {format(new Date(inquiry.created_at), "PPpp")}
                      </TableCell>
                      <TableCell>
                        {inquiry.profiles?.first_name || "N/A"} {inquiry.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell>{inquiry.subject}</TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {inquiry.message}
                      </TableCell>
                      <TableCell className="capitalize">{inquiry.status}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(inquiry)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Inquiry Status Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Update Inquiry Status</DialogTitle>
            <DialogDescription>
              Update the status for the inquiry from {currentInquiry?.profiles?.first_name} {currentInquiry?.profiles?.last_name} regarding "{currentInquiry?.subject}".
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateInquiryStatus} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="inquiryMessage">Message</Label>
              <Textarea
                id="inquiryMessage"
                value={currentInquiry?.message || ""}
                readOnly
                rows={6}
                className="bg-gray-100 dark:bg-gray-800"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editStatus">Status</Label>
              <Select onValueChange={setEditStatus} value={editStatus}>
                <SelectTrigger id="editStatus">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Open">Open</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Resolved">Resolved</SelectItem>
                  <SelectItem value="Closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>

          <div className="mt-6 pt-4 border-t">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              Comments
            </h3>
            <div className="space-y-3 max-h-48 overflow-y-auto pr-2">
              {inquiryComments.length === 0 ? (
                <p className="text-sm text-muted-foreground">No comments yet.</p>
              ) : (
                inquiryComments.map((comment) => (
                  <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                    <p className="text-sm font-medium">
                      {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                      <span className="text-xs text-muted-foreground ml-2">
                        ({comment.profiles?.role || "N/A"})
                      </span>
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(comment.created_at), "PPpp")}
                    </p>
                    <p className="text-sm mt-1">{comment.comment_text}</p>
                  </div>
                ))
              )}
            </div>
            <form onSubmit={handleAddComment} className="mt-4 flex gap-2">
              <Textarea
                placeholder="Add a new comment..."
                value={newCommentText}
                onChange={(e) => setNewCommentText(e.target.value)}
                rows={2}
                className="flex-1"
                disabled={loading}
              />
              <Button type="submit" disabled={loading}>
                Add Comment
              </Button>
            </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FieldOfficerClientInquiries;