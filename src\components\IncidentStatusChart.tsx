import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface IncidentStatusData {
  name: string;
  value: number;
}

interface IncidentStatusChartProps {
  data: IncidentStatusData[];
}

const COLORS = {
  'Open': '#0088FE', // Blue
  'In Progress': '#FFBB28', // Yellow/Orange
  'Resolved': '#00C49F', // Green
  'Closed': '#8884d8', // Purple (for closed, if applicable)
  'Cancelled': '#FF8042', // Orange (for cancelled, if applicable)
};

const IncidentStatusChart: React.FC<IncidentStatusChartProps> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={100}
          fill="#8884d8"
          dataKey="value"
          nameKey="name"
          label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[entry.name as keyof typeof COLORS] || '#CCCCCC'} />
          ))}
        </Pie>
        <Tooltip />
        <Legend />
      </PieChart>
    </ResponsiveContainer>
  );
};

export default IncidentStatusChart;