import React from 'react';
import { useAuth } from '@/hooks/useAuth'; // Corrected import path
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const AuthDebug: React.FC = () => {
  const { session, user, profile, organization, plan, loading, error, connectionStatus } = useAuth();

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <Card className="fixed bottom-4 right-4 w-80 max-h-96 overflow-auto z-50 bg-white dark:bg-gray-800 border-2 border-blue-500">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">Auth Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="text-xs space-y-2">
        <div>
          <strong>Connection Status:</strong> 
          <span className={`ml-1 px-2 py-1 rounded text-white ${
            connectionStatus === 'connected' ? 'bg-green-500' : 
            connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
          }`}>
            {connectionStatus}
          </span>
        </div>
        
        <div>
          <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
        </div>
        
        {error && (
          <div>
            <strong>Error:</strong> 
            <div className="text-red-600 bg-red-50 p-1 rounded mt-1">{error}</div>
          </div>
        )}
        
        <div>
          <strong>Session:</strong> {session ? 'Present' : 'None'}
        </div>
        
        <div>
          <strong>User ID:</strong> {user?.id || 'None'}
        </div>
        
        <div>
          <strong>User Email:</strong> {user?.email || 'None'}
        </div>
        
        <div>
          <strong>Profile:</strong> {profile ? 'Present' : 'None'}
        </div>
        
        {profile && (
          <div>
            <strong>Role:</strong> {profile.role || 'None'}
          </div>
        )}
        
        <div>
          <strong>Organization:</strong> {organization ? 'Present' : 'None'}
        </div>
        
        {organization && (
          <div>
            <strong>Org Name:</strong> {organization.name || 'None'}
          </div>
        )}
        
        <div>
          <strong>Plan:</strong> {plan ? plan.name : 'None'}
        </div>
        
        <div>
          <strong>Environment:</strong>
          <div className="ml-2">
            <div>URL: {import.meta.env.VITE_SUPABASE_URL ? '✓' : '✗'}</div>
            <div>Key: {import.meta.env.VITE_SUPABASE_ANON_KEY ? '✓' : '✗'}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};