import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";
import { format, isFuture, parseISO } from "date-fns";
import { Button } from "@/components/ui/button";
import { AlertTriangle, MapPin, Clock, ListTodo, BellRing, MessageCircle, Banknote } from "lucide-react"; // Added Banknote icon
import { Switch } from "@/components/ui/switch"; // Import Switch component
import { Label } from "@/components/ui/label"; // Import Label component

interface Shift {
  id: string;
  user_id: string;
  shift_date: string;
  start_time: string;
  end_time: string;
  location: string;
  created_at: string;
}

interface Incident {
  id: string;
  title: string;
  created_at: string;
  type: "incident";
}

interface Timesheet {
  id: string;
  hours_worked: number;
  shift_date: string;
  created_at: string;
  type: "timesheet";
}

interface Task {
  id: string;
  title: string;
  status: string;
  created_at: string;
  type: "task";
}

interface SalaryPayment {
  id: string;
  amount: number;
  payment_date: string;
  status: string;
  created_at: string;
  type: "salary_payment";
}

type RecentActivity = Incident | Timesheet | Task | SalaryPayment;

const GuardDashboard = () => {
  const { user, loading: authLoading } = useAuth();
  const [incidentsReported, setIncidentsReported] = useState<number | null>(null);
  const [loadingIncidents, setLoadingIncidents] = useState(true);
  const [upcomingShiftsCount, setUpcomingShiftsCount] = useState<number | null>(null);
  const [nextShiftTime, setNextShiftTime] = useState<string | null>(null);
  const [loadingShifts, setLoadingShifts] = useState(true);
  const [pendingTasksCount, setPendingTasksCount] = useState<number | null>(null);
  const [loadingTasks, setLoadingTasks] = useState(true);
  const [activePanicAlertsCount, setActivePanicAlertsCount] = useState<number | null>(null);
  const [loadingPanicAlerts, setLoadingPanicAlerts] = useState(true);
  const [openSupportChatsCount, setOpenSupportChatsCount] = useState<number | null>(null);
  const [loadingSupportChats, setLoadingSupportChats] = useState(true);
  const [panicLoading, setPanicLoading] = useState(false);
  const [isTrackingLocation, setIsTrackingLocation] = useState(false); // State for the toggle
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [loadingActivities, setLoadingActivities] = useState(true);
  const [sendLocationLoading, setSendLocationLoading] = useState(false);

  useEffect(() => {
    const fetchIncidentsCount = async () => {
      if (!user) {
        setLoadingIncidents(false);
        return;
      }

      setLoadingIncidents(true);
      const { count, error } = await supabase
        .from("incidents")
        .select("*", { count: "exact", head: true })
        .eq("user_id", user.id);

      if (error) {
        toast.error("Failed to fetch incident count: " + error.message);
        console.error("Error fetching incident count:", error);
        setIncidentsReported(0);
      } else {
        setIncidentsReported(count);
      }
      setLoadingIncidents(false);
    };

    const fetchUpcomingShifts = async () => {
      if (!user) {
        setLoadingShifts(false);
        return;
      }

      setLoadingShifts(true);
      const { data, error } = await supabase
        .from("shifts")
        .select("*")
        .eq("user_id", user.id)
        .order("shift_date", { ascending: true })
        .order("start_time", { ascending: true });

      if (error) {
        toast.error("Failed to load shifts: " + error.message);
        console.error("Error fetching shifts:", error);
        setUpcomingShiftsCount(0);
        setNextShiftTime(null);
      } else {
        const now = new Date();
        const futureShifts = (data as Shift[]).filter(shift => {
          const shiftDateTime = parseISO(`${shift.shift_date}T${shift.start_time}`);
          return isFuture(shiftDateTime);
        });

        setUpcomingShiftsCount(futureShifts.length);

        if (futureShifts.length > 0) {
          const nextShift = futureShifts[0];
          const nextShiftDateTime = parseISO(`${nextShift.shift_date}T${nextShift.start_time}`);
          setNextShiftTime(format(nextShiftDateTime, "MMM dd, hh:mm a"));
        } else {
          setNextShiftTime(null);
        }
      }
      setLoadingShifts(false);
    };

    const fetchPendingTasksCount = async () => {
      if (!user) {
        setLoadingTasks(false);
        return;
      }

      setLoadingTasks(true);
      const { count, error } = await supabase
        .from("tasks")
        .select("*", { count: "exact", head: true })
        .in("status", ["Pending", "In Progress"]); // Count tasks that are not yet completed or cancelled

      if (error) {
        toast.error("Failed to fetch pending tasks count: " + error.message);
        console.error("Error fetching pending tasks count:", error);
        setPendingTasksCount(0);
      } else {
        setPendingTasksCount(count);
      }
      setLoadingTasks(false);
    };

    const fetchActivePanicAlertsCount = async () => {
      if (!user) {
        setLoadingPanicAlerts(false);
        return;
      }

      setLoadingPanicAlerts(true);
      const { count, error } = await supabase
        .from("panic_alerts")
        .select("*", { count: "exact", head: true })
        .eq("user_id", user.id)
        .eq("status", "Active");

      if (error) {
        toast.error("Failed to fetch active panic alerts count: " + error.message);
        console.error("Error fetching active panic alerts count:", error);
        setActivePanicAlertsCount(0);
      } else {
        setActivePanicAlertsCount(count);
      }
      setLoadingPanicAlerts(false);
    };

    const fetchOpenSupportChatsCount = async () => {
      if (!user) {
        setLoadingSupportChats(false);
        return;
      }

      setLoadingSupportChats(true);
      const { count, error } = await supabase
        .from("support_chats")
        .select("*", { count: "exact", head: true })
        .eq("user_id", user.id)
        .in("status", ["Open", "In Progress"]);

      if (error) {
        toast.error("Failed to fetch open support chats count: " + error.message);
        console.error("Error fetching open support chats count:", error);
        setOpenSupportChatsCount(0);
      } else {
        setOpenSupportChatsCount(count);
      }
      setLoadingSupportChats(false);
    };

    const fetchRecentActivities = async () => {
      if (!user) {
        setLoadingActivities(false);
        return;
      }

      setLoadingActivities(true);
      const [incidentsRes, timesheetsRes, tasksRes, salaryPaymentsRes] = await Promise.all([
        supabase.from("incidents").select("id, title, created_at").eq("user_id", user.id).order("created_at", { ascending: false }).limit(5),
        supabase.from("timesheets").select("id, hours_worked, shift_date, created_at").eq("user_id", user.id).order("created_at", { ascending: false }).limit(5),
        supabase.from("tasks").select("id, title, status, created_at").eq("user_id", user.id).order("created_at", { ascending: false }).limit(5),
        supabase.from("salary_payments").select("id, amount, payment_date, status, created_at").eq("user_id", user.id).order("created_at", { ascending: false }).limit(5), // Fetch salary payments
      ]);

      const activities: RecentActivity[] = [];

      if (incidentsRes.data) {
        incidentsRes.data.forEach(item => activities.push({ ...item, type: "incident" }));
      }
      if (timesheetsRes.data) {
        timesheetsRes.data.forEach(item => activities.push({ ...item, type: "timesheet" }));
      }
      if (tasksRes.data) {
        tasksRes.data.forEach(item => activities.push({ ...item, type: "task" }));
      }
      if (salaryPaymentsRes.data) {
        salaryPaymentsRes.data.forEach(item => activities.push({ ...item, type: "salary_payment" })); // Add salary payments
      }

      activities.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      setRecentActivities(activities.slice(0, 5)); // Show top 5 recent activities
      setLoadingActivities(false);
    };

    if (!authLoading) {
      fetchIncidentsCount();
      fetchUpcomingShifts();
      fetchPendingTasksCount();
      fetchActivePanicAlertsCount();
      fetchOpenSupportChatsCount();
      fetchRecentActivities();
    }
  }, [user, authLoading]);

  useEffect(() => {
    let watchId: number | null = null;

    const startTracking = () => {
      if (!user || !navigator.geolocation) {
        console.warn("Geolocation not supported or user not logged in. Cannot start tracking.");
        setIsTrackingLocation(false); // Ensure UI reflects actual state if conditions aren't met
        return;
      }

      toast.info("Starting live location tracking...");

      watchId = navigator.geolocation.watchPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          console.log("Sending location update:", latitude, longitude);
          const { error } = await supabase.from("guard_locations").upsert(
            {
              user_id: user.id,
              latitude: latitude,
              longitude: longitude,
              timestamp: new Date().toISOString(),
            },
            { onConflict: 'user_id' } // Update if user_id exists, otherwise insert
          );

          if (error) {
            console.error("Failed to update location:", error.message);
            toast.error("Failed to update location: " + error.message);
          }
        },
        (error) => {
          console.error("Geolocation watch error:", error);
          toast.error("Location tracking error: " + error.message);
          setIsTrackingLocation(false); // Turn off switch if error occurs
        },
        {
          enableHighAccuracy: true,
          maximumAge: 10000, // Accept cached position up to 10 seconds old
          timeout: 5000, // Wait up to 5 seconds for a position
        }
      );
    };

    const stopTracking = () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
        watchId = null;
        toast.info("Location tracking stopped.");
      }
    };

    // Logic to start/stop based on isTrackingLocation state
    if (isTrackingLocation && user) { // Only start if user wants it AND is logged in
      startTracking();
    } else {
      stopTracking(); // Stop if not logged in or tracking is disabled
    }

    return () => {
      stopTracking(); // Cleanup on unmount
    };
  }, [user, isTrackingLocation]); // Dependencies: user and the new isTrackingLocation state

  const handlePanicAlert = async () => {
    if (!user) {
      toast.error("You must be logged in to send a panic alert.");
      return;
    }

    setPanicLoading(true);
    let latitude: number | null = null;
    let longitude: number | null = null;

    if (navigator.geolocation) {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          });
        });
        latitude = position.coords.latitude;
        longitude = position.coords.longitude;
        toast.info("Location captured for panic alert.");
      } catch (geoError: any) {
        console.error("Geolocation error:", geoError);
        if (geoError.code === geoError.PERMISSION_DENIED) {
          toast.warning("Location access denied. Panic alert sent without location.");
        } else if (geoError.code === geoError.POSITION_UNAVAILABLE) {
          toast.warning("Location information unavailable. Panic alert sent without location.");
        } else if (geoError.code === geoError.TIMEOUT) {
          toast.warning("Location request timed out. Panic alert sent without location.");
        } else {
          toast.error("Failed to get location: " + geoError.message);
        }
      }
    } else {
      toast.warning("Geolocation is not supported by your browser. Panic alert sent without location.");
    }

    const { error } = await supabase.from("panic_alerts").insert({
      user_id: user.id,
      status: "Active",
      latitude: latitude,
      longitude: longitude,
    });

    if (error) {
      toast.error("Failed to send panic alert: " + error.message);
      console.error("Panic alert submission error:", error);
    } else {
      toast.success("Panic alert sent! Field officers have been notified.");
    }
    setPanicLoading(false);
  };

  const handleSendLocation = async () => {
    if (!user) {
      toast.error("You must be logged in to send your location.");
      return;
    }

    setSendLocationLoading(true);
    if (navigator.geolocation) {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          });
        });
        const { latitude, longitude } = position.coords;

        const { error } = await supabase.from("guard_locations").upsert(
          {
            user_id: user.id,
            latitude: latitude,
            longitude: longitude,
            timestamp: new Date().toISOString(),
          },
          { onConflict: 'user_id' }
        );

        if (error) {
          toast.error("Failed to send location: " + error.message);
          console.error("Manual location send error:", error);
        } else {
          toast.success("Your current location has been sent!");
        }
      } catch (geoError: any) {
        console.error("Geolocation error:", geoError);
        if (geoError.code === geoError.PERMISSION_DENIED) {
          toast.warning("Location access denied. Cannot send location.");
        } else if (geoError.code === geoError.POSITION_UNAVAILABLE) {
          toast.warning("Location information unavailable. Cannot send location.");
        } else if (geoError.code === geoError.TIMEOUT) {
          toast.warning("Location request timed out. Cannot send location.");
        } else {
          toast.error("Failed to get location: " + geoError.message);
        }
      }
    } else {
      toast.warning("Geolocation is not supported by your browser.");
    }
    setSendLocationLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Guard Dashboard</h1>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Upcoming Shifts
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingShifts ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{upcomingShiftsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              {nextShiftTime ? `Next shift: ${nextShiftTime}` : "No upcoming shifts"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Incidents Reported
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingIncidents ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{incidentsReported}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Total incidents reported by you
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Tasks Pending
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingTasks ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{pendingTasksCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Assigned tasks awaiting completion
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Panic Alerts
            </CardTitle>
            <BellRing className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingPanicAlerts ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{activePanicAlertsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Alerts awaiting field officer action
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Open Support Chats
            </CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingSupportChats ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{openSupportChatsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Active conversations with support
            </p>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          {loadingActivities ? (
            <p className="text-sm text-muted-foreground">Loading recent activity...</p>
          ) : recentActivities.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent activity to display.</p>
          ) : (
            <div className="space-y-3">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3">
                  {activity.type === "incident" && <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />}
                  {activity.type === "timesheet" && <Clock className="h-4 w-4 text-blue-500 flex-shrink-0" />}
                  {activity.type === "task" && <ListTodo className="h-4 w-4 text-green-500 flex-shrink-0" />}
                  {activity.type === "salary_payment" && <Banknote className="h-4 w-4 text-green-500 flex-shrink-0" />}
                  <div className="flex-1">
                    <p className="text-sm font-medium">
                      {activity.type === "incident" && `Reported Incident: ${activity.title}`}
                      {activity.type === "timesheet" && `Submitted Timesheet: ${activity.hours_worked} hours on ${format(new Date(activity.shift_date), "MMM dd")}`}
                      {activity.type === "task" && `Task Update: "${activity.title}" is now ${activity.status}`}
                      {activity.type === "salary_payment" && `Salary Payment: ₹${activity.amount.toFixed(2)} (${activity.status})`}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(activity.created_at), "PPpp")}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="border-red-500 border-2">
        <CardHeader>
          <CardTitle className="text-red-600 flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5" />
            Emergency Alert
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            In case of an emergency, press the button below to send an immediate distress signal to field officers.
          </p>
          <Button
            variant="destructive"
            className="w-full py-3 text-lg font-bold"
            onClick={handlePanicAlert}
            disabled={panicLoading}
          >
            {panicLoading ? "Sending Alert..." : "PANIC BUTTON"}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="mr-2 h-5 w-5 text-blue-500" />
            Location Tracking Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between space-x-2 mb-4">
            <Label htmlFor="location-tracking-switch" className="text-sm font-medium">
              Enable Live Location Tracking
            </Label>
            <Switch
              id="location-tracking-switch"
              checked={isTrackingLocation}
              onCheckedChange={setIsTrackingLocation}
              disabled={!user} // Disable if not logged in
            />
          </div>
          <p className="text-sm text-muted-foreground">
            {isTrackingLocation
              ? "Your location is currently being tracked for safety and operational purposes."
              : "Location tracking is currently disabled. Enable it to share your live position."}
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            Location data is only collected while tracking is enabled and you are logged in.
          </p>
          <Button
            onClick={handleSendLocation}
            disabled={sendLocationLoading || !user}
            className="mt-4 w-full"
          >
            {sendLocationLoading ? "Sending Location..." : "Send My Current Location (Manual)"}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default GuardDashboard;