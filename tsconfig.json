{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path Aliases */
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"]
    },
    "esModuleInterop": true
  },
  "include": ["src", "tailwind.config.ts", "postcss.config.js", "vitest.config.ts", "supabase/functions/**/*.ts"],
  "references": [{ "path": "./tsconfig.node.json" }]
}