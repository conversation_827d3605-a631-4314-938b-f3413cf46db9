import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, ShieldCheck, Briefcase, BarChart2, AlertCircle, Clock, CalendarDays } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";

const AdminDashboard = () => {
  const [totalUsers, setTotalUsers] = useState<number | null>(null);
  const [totalIncidents, setTotalIncidents] = useState<number | null>(null);
  const [totalShifts, setTotalShifts] = useState<number | null>(null);
  const [totalTimesheets, setTotalTimesheets] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      let hasError = false;

      // Fetch Total Users
      const { count: usersCount, error: usersError } = await supabase
        .from("profiles")
        .select("*", { count: "exact", head: true });
      if (usersError) {
        console.error("Error fetching total users:", usersError);
        setTotalUsers(0);
        hasError = true;
      } else {
        setTotalUsers(usersCount);
      }

      // Fetch Total Incidents
      const { count: incidentsCount, error: incidentsError } = await supabase
        .from("incidents")
        .select("*", { count: "exact", head: true });
      if (incidentsError) {
        console.error("Error fetching total incidents:", incidentsError);
        setTotalIncidents(0);
        hasError = true;
      } else {
        setTotalIncidents(incidentsCount);
      }

      // Fetch Total Shifts
      const { count: shiftsCount, error: shiftsError } = await supabase
        .from("shifts")
        .select("*", { count: "exact", head: true });
      if (shiftsError) {
        console.error("Error fetching total shifts:", shiftsError);
        setTotalShifts(0);
        hasError = true;
      } else {
        setTotalShifts(shiftsCount);
      }

      // Fetch Total Timesheets
      const { count: timesheetsCount, error: timesheetsError } = await supabase
        .from("timesheets")
        .select("*", { count: "exact", head: true });
      if (timesheetsError) {
        console.error("Error fetching total timesheets:", timesheetsError);
        setTotalTimesheets(0);
        hasError = true;
      } else {
        setTotalTimesheets(timesheetsCount);
      }

      setLoading(false);
      if (hasError) {
        toast.error("Some dashboard data could not be loaded.");
      }
    };

    fetchDashboardData();

    // Set up real-time subscriptions for relevant tables
    const profilesChannel = supabase
      .channel('admin_dashboard_profiles_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'profiles' }, () => {
        fetchDashboardData();
      })
      .subscribe();

    const incidentsChannel = supabase
      .channel('admin_dashboard_incidents_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'incidents' }, () => {
        fetchDashboardData();
      })
      .subscribe();

    const shiftsChannel = supabase
      .channel('admin_dashboard_shifts_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'shifts' }, () => {
        fetchDashboardData();
      })
      .subscribe();

    const timesheetsChannel = supabase
      .channel('admin_dashboard_timesheets_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'timesheets' }, () => {
        fetchDashboardData();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(profilesChannel);
      supabase.removeChannel(incidentsChannel);
      supabase.removeChannel(shiftsChannel);
      supabase.removeChannel(timesheetsChannel);
    };
  }, []);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Company Admin Dashboard</h1>
      <p className="text-muted-foreground">
        Welcome, Administrator. Here's an overview of your company's operations.
      </p>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Users
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{totalUsers}</div>
            )}
            <p className="text-xs text-muted-foreground">
              All registered user accounts
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link to="/field-officer/user-management">Manage Users</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Incidents
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{totalIncidents}</div>
            )}
            <p className="text-xs text-muted-foreground">
              All security incidents reported
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link to="/field-officer/incidents">View Incidents</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Shifts Scheduled
            </CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{totalShifts}</div>
            )}
            <p className="text-xs text-muted-foreground">
              All shifts scheduled across the system
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link to="/field-officer/shift-schedule-overview">View Shifts</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Timesheets Submitted
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{totalTimesheets}</div>
            )}
            <p className="text-xs text-muted-foreground">
              All timesheet entries from guards
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link to="/field-officer/timesheets">View Timesheets</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Company Reports
            </CardTitle>
            <BarChart2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link to="/admin/company-reports">View Reports</Link>
            </Button>
            <p className="text-xs text-muted-foreground">
              Access high-level operational reports.
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              System Settings
            </CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link to="/admin/system-settings">Configure Settings</Link>
            </Button>
            <p className="text-xs text-muted-foreground">
              Configure global application settings.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;