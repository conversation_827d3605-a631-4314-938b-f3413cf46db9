import { <PERSON> } from "react-router-dom";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Home, Shield, Users, MapPin, AlertCircle, MessageSquareText, UserCog, CalendarDays, BarChart2, LogOut, Clock, MessageSquarePlus, CalendarPlus, CalendarCheck, User as UserIcon, FileText, MessageCircleQuestion, ListChecks, BellRing, ListTodo, Brain, MessageCircle, TrendingUp, Settings, ReceiptText, Package, DollarSign, Banknote, Wallet, HelpCircle } from "lucide-react"; // Added HelpCircle icon
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const { session, loading, profile } = useAuth();

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      toast.error("Logout failed: " + error.message);
    } else {
      toast.success("Logged out successfully!");
    }
  };

  if (loading) {
    return (
      <div className={cn("pb-12 flex items-center justify-center", className)}>
        <p className="text-sm text-muted-foreground">Loading...</p>
      </div>
    );
  }

  return (
    <div className={cn("pb-12", className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            MirazSec
          </h2>
          <div className="space-y-1">
            {session ? (
              <>
                {/* Profile link for all authenticated users */}
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link to="/profile">
                    <span className="flex items-center">
                      <Avatar className="mr-2 h-6 w-6">
                        <AvatarImage src={profile?.avatar_url || ""} alt={`${profile?.first_name} ${profile?.last_name}'s avatar`} />
                        <AvatarFallback>
                          {profile?.first_name ? profile.first_name.charAt(0) : ''}
                          {profile?.last_name ? profile.last_name.charAt(0) : ''}
                        </AvatarFallback>
                      </Avatar>
                      My Profile
                    </span>
                  </Link>
                </Button>

                {profile?.role === "company_admin" && (
                  <>
                    <h3 className="mt-4 mb-2 px-4 text-sm font-semibold tracking-tight">
                      Admin Tools
                    </h3>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/admin">
                        <span className="flex items-center">
                          <Settings className="mr-2 h-4 w-4" />
                          Admin Dashboard
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/admin/company-reports">
                        <span className="flex items-center">
                          <BarChart2 className="mr-2 h-4 w-4" />
                          Company Reports
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/admin/system-settings">
                        <span className="flex items-center">
                          <Settings className="mr-2 h-4 w-4" />
                          System Settings
                        </span>
                      </Link>
                    </Button>
                  </>
                )}

                {profile?.role === "guard" && (
                  <>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard">
                        <span className="flex items-center">
                          <Home className="mr-2 h-4 w-4" />
                          Guard Dashboard
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/report-incident">
                        <span className="flex items-center">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          Report Incident
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/shift-schedule">
                        <span className="flex items-center">
                          <CalendarDays className="mr-2 h-4 w-4" />
                          My Schedule
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/submit-timesheet">
                        <span className="flex items-center">
                          <Clock className="mr-2 h-4 w-4" />
                          Submit Timesheet
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/timesheets">
                        <span className="flex items-center">
                          <FileText className="mr-2 h-4 w-4" />
                          My Timesheets
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/incidents">
                        <span className="flex items-center">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          My Incidents
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/communications">
                        <span className="flex items-center">
                          <MessageSquareText className="mr-2 h-4 w-4" />
                          My Communications
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/my-tasks">
                        <span className="flex items-center">
                          <ListTodo className="mr-2 h-4 w-4" />
                          My Tasks
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/panic-alerts">
                        <span className="flex items-center">
                          <BellRing className="mr-2 h-4 w-4 text-red-500" />
                          My Panic Alerts
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/incident-insights">
                        <span className="flex items-center">
                          <Brain className="mr-2 h-4 w-4" />
                          Incident Insights
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/guard/support-chat">
                        <span className="flex items-center">
                          <MessageCircle className="mr-2 h-4 w-4" />
                          Support Chat
                        </span>
                      </Link>
                    </Button>
                  </>
                )}
                {profile?.role === "client" && (
                  <>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client">
                        <span className="flex items-center">
                          <Users className="mr-2 h-4 w-4" />
                          Client Dashboard
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client/report-incident">
                        <span className="flex items-center">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          Report Incident
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client/submit-inquiry">
                        <span className="flex items-center">
                          <MessageCircleQuestion className="mr-2 h-4 w-4" />
                          Submit Inquiry
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client/my-inquiries">
                        <span className="flex items-center">
                          <ListChecks className="mr-2 h-4 w-4" />
                          My Inquiries
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client/incidents-comms">
                        <span className="flex items-center">
                          <MessageSquareText className="mr-2 h-4 w-4" />
                          Incidents & Comms
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client/guard-locations">
                        <span className="flex items-center">
                          <MapPin className="mr-2 h-4 w-4" />
                          Guard Locations
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client/reports">
                        <span className="flex items-center">
                          <BarChart2 className="mr-2 h-4 w-4" />
                          Reports & Analytics
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/client/invoices">
                        <span className="flex items-center">
                          <ReceiptText className="mr-2 h-4 w-4" />
                          My Invoices
                        </span>
                      </Link>
                    </Button>
                  </>
                )}
                {profile?.role === "field_officer" && (
                  <>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer">
                        <span className="flex items-center">
                          <MapPin className="mr-2 h-4 w-4" />
                          Field Officer Dashboard
                        </span>
                      </Link>
                    </Button>
                    <h3 className="mt-4 mb-2 px-4 text-sm font-semibold tracking-tight">
                      Operations
                    </h3>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/shift-scheduling">
                        <span className="flex items-center">
                          <CalendarPlus className="mr-2 h-4 w-4" />
                          Schedule Shift
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/shift-schedule-overview">
                        <span className="flex items-center">
                          <CalendarCheck className="mr-2 h-4 w-4" />
                          Shift Overview
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/create-communication">
                        <span className="flex items-center">
                          <MessageSquarePlus className="mr-2 h-4 w-4" />
                          Create Communication
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/schedule-communication">
                        <span className="flex items-center">
                          <CalendarPlus className="mr-2 h-4 w-4" />
                          Schedule Communication
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/communications-overview">
                        <span className="flex items-center">
                          <MessageSquareText className="mr-2 h-4 w-4" />
                          Communications Overview
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/incidents">
                        <span className="flex items-center">
                          <AlertCircle className="mr-2 h-4 w-4" />
                          Incident Overview
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/panic-alerts">
                        <span className="flex items-center">
                          <BellRing className="mr-2 h-4 w-4 text-red-500" />
                          Panic Alerts
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/create-task">
                        <span className="flex items-center">
                          <ListTodo className="mr-2 h-4 w-4" />
                          Create Task
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/tasks-overview">
                        <span className="flex items-center">
                          <ListChecks className="mr-2 h-4 w-4" />
                          Tasks Overview
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/guard-locations">
                        <span className="flex items-center">
                          <MapPin className="mr-2 h-4 w-4" />
                          Guard Locations
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/support-chat-overview">
                        <span className="flex items-center">
                          <MessageCircle className="mr-2 h-4 w-4" />
                          Support Chats
                        </span>
                      </Link>
                    </Button>

                    <h3 className="mt-4 mb-2 px-4 text-sm font-semibold tracking-tight">
                      Management & Analytics
                    </h3>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/user-management">
                        <span className="flex items-center">
                          <UserCog className="mr-2 h-4 w-4" />
                          User Management
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/timesheets">
                        <span className="flex items-center">
                          <Clock className="mr-2 h-4 w-4" />
                          Timesheet Overview
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/client-inquiries">
                        <span className="flex items-center">
                          <MessageCircleQuestion className="mr-2 h-4 w-4" />
                          Client Inquiries
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/reports">
                        <span className="flex items-center">
                          <BarChart2 className="mr-2 h-4 w-4" />
                          Reports
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/predictive-risk">
                        <span className="flex items-center">
                          <TrendingUp className="mr-2 h-4 w-4" />
                          Predictive Risk
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/manage-services">
                        <span className="flex items-center">
                          <Package className="mr-2 h-4 w-4" />
                          Manage Services
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/create-invoice">
                        <span className="flex items-center">
                          <DollarSign className="mr-2 h-4 w-4" />
                          Create Invoice
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/initiate-payout">
                        <span className="flex items-center">
                          <Banknote className="mr-2 h-4 w-4" />
                          Initiate Payout
                        </span>
                      </Link>
                    </Button>
                    <Button variant="ghost" className="w-full justify-start" asChild>
                      <Link to="/field-officer/salary-payments-overview">
                        <span className="flex items-center">
                          <Banknote className="mr-2 h-4 w-4" />
                          Salary Payments
                        </span>
                      </Link>
                    </Button>
                  </>
                )}
                {/* Logout button for authenticated users */}
                <Button variant="ghost" className="w-full justify-start" onClick={handleLogout}>
                  <span className="flex items-center">
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </span>
                </Button>
              </>
            ) : (
              <>
                {/* Login and Sign Up buttons for unauthenticated users */}
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link to="/login">
                    <span className="flex items-center">
                      <LogOut className="mr-2 h-4 w-4 rotate-180" />
                      Login
                    </span>
                  </Link>
                </Button>
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <Link to="/signup">
                    <span className="flex items-center">
                      <UserCog className="mr-2 h-4 w-4" />
                      Sign Up
                    </span>
                  </Link>
                </Button>
              </>
            )}
          </div>
        </div>
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            General
          </h2>
          <div className="space-y-1">
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link to="/">
                <span className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  Home
                </span>
              </Link>
            </Button>
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link to="/why-choose-us">
                <span className="flex items-center">
                  <HelpCircle className="mr-2 h-4 w-4" />
                  Why Choose Us
                </span>
              </Link>
            </Button>
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link to="/plan">
                <span className="flex items-center">
                  <Wallet className="mr-2 h-4 w-4" /> {/* Using Wallet icon for Plan */}
                  Plan
                </span>
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}