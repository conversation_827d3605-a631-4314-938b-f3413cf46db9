import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { MessageSquare, <PERSON><PERSON><PERSON>riangle, MessageCircleQuestion } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { format, subDays } from "date-fns"; // Import subDays
import { useAuth } from "@/context/AuthContext";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useOrganization } from "@/hooks/useOrganization"; // Import useOrganization hook

interface Incident {
  id: string;
  user_id: string;
  title: string;
  description: string;
  location: string;
  image_url: string | null;
  created_at: string;
  status: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
  incident_comments: IncidentComment[];
}

interface IncidentComment {
  id: string;
  incident_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

interface Communication {
  id: string;
  title: string;
  content: string; // Content is now HTML
  created_at: string;
  target_roles: string[]; // Added target_roles
}

interface ClientInquiry {
  id: string;
  client_id: string;
  subject: string;
  message: string;
  status: string;
  created_at: string;
  client_inquiry_comments: InquiryComment[];
}

interface InquiryComment {
  id: string;
  inquiry_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

const ClientIncidentsAndComms = () => {
  const { user, loading: authLoading, profile } = useAuth(); // Get profile
  const { plan, loading: orgLoading, canAccessFullIncidentHistory } = useOrganization(); // Use the hook
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loadingIncidents, setLoadingIncidents] = useState(true);
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [loadingCommunications, setLoadingCommunications] = useState(true);
  const [inquiries, setInquiries] = useState<ClientInquiry[]>([]);
  const [loadingInquiries, setLoadingInquiries] = useState(true);
  const [newInquiryCommentText, setNewInquiryCommentText] = useState<{ [key: string]: string }>({});
  const [inquiryCommentLoading, setInquiryCommentLoading] = useState<{ [key: string]: boolean }>({});
  const [newIncidentCommentText, setNewIncidentCommentText] = useState<{ [key: string]: string }>({});
  const [incidentCommentLoading, setIncidentCommentLoading] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const fetchIncidents = async () => {
      if (!user) {
        setLoadingIncidents(false);
        return;
      }
      setLoadingIncidents(true);
      let query = supabase
        .from("incidents")
        .select(`
          *,
          profiles (
            first_name,
            last_name
          ),
          incident_comments (
            id,
            user_id,
            comment_text,
            created_at,
            profiles (
              first_name,
              last_name,
              role
            )
          )
        `)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      // Apply incident history limit based on plan
      if (!canAccessFullIncidentHistory && plan?.limits.incidentHistoryDays) {
        const cutoffDate = subDays(new Date(), plan.limits.incidentHistoryDays);
        query = query.gte("created_at", cutoffDate.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        toast.error("Failed to load incidents: " + error.message);
        console.error("Error fetching incidents for client:", error);
        setIncidents([]);
      } else {
        const incidentsWithSortedComments = (data as Incident[]).map(incident => ({
          ...incident,
          incident_comments: incident.incident_comments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        }));
        setIncidents(incidentsWithSortedComments);
      }
      setLoadingIncidents(false);
    };

    const fetchCommunications = async () => {
      if (!profile?.role) { // Wait for profile to load
        setLoadingCommunications(false);
        return;
      }
      setLoadingCommunications(true);
      const { data, error } = await supabase
        .from("communications")
        .select("*")
        .contains("target_roles", [profile.role]) // Filter by user's role
        .order("created_at", { ascending: false });

      if (error) {
        toast.error("Failed to load communications: " + error.message);
        console.error("Error fetching communications:", error);
      } else {
        setCommunications(data as Communication[]);
      }
      setLoadingCommunications(false);
    };

    const fetchInquiries = async () => {
      if (!user) {
        setLoadingInquiries(false);
        return;
      }

      setLoadingInquiries(true);
      const { data, error } = await supabase
        .from("client_inquiries")
        .select(`
          *,
          client_inquiry_comments (
            id,
            user_id,
            comment_text,
            created_at,
            profiles (
              first_name,
              last_name,
              role
            )
          )
        `)
        .eq("client_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        toast.error("Failed to load your inquiries: " + error.message);
        console.error("Error fetching client inquiries:", error);
        setInquiries([]);
      } else {
        const inquiriesWithSortedComments = (data as ClientInquiry[]).map(inquiry => ({
          ...inquiry,
          client_inquiry_comments: inquiry.client_inquiry_comments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        }));
        setInquiries(inquiriesWithSortedComments);
      }
      setLoadingInquiries(false);
    };

    if (!authLoading && !orgLoading) { // Ensure auth and org data are loaded
      fetchIncidents();
      fetchCommunications();
      fetchInquiries();
    }

    const incidentsChannel = supabase
      .channel('client_incidents_and_comms_incidents_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incidents' },
        () => {
          fetchIncidents();
        }
      )
      .subscribe();

    const incidentCommentsChannel = supabase
      .channel('client_incidents_and_comms_incident_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incident_comments' },
        () => {
          fetchIncidents(); // Re-fetch incidents to update comments
        }
      )
      .subscribe();

    const inquiryCommentsChannel = supabase
      .channel('client_inquiry_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'client_inquiry_comments' },
        () => {
          fetchInquiries();
        }
      )
      .subscribe();

    const communicationsChannel = supabase
      .channel('client_incidents_and_comms_communications_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'communications' },
        (payload) => {
          if (profile?.role && payload.new.target_roles?.includes(profile.role)) {
            fetchCommunications();
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(incidentsChannel);
      supabase.removeChannel(incidentCommentsChannel);
      supabase.removeChannel(inquiryCommentsChannel);
      supabase.removeChannel(communicationsChannel);
    };
  }, [user, authLoading, profile, orgLoading, canAccessFullIncidentHistory, plan]); // Depend on user, authLoading, profile, orgLoading, canAccessFullIncidentHistory, and plan

  const handleAddInquiryComment = async (inquiryId: string) => {
    const commentText = newInquiryCommentText[inquiryId]?.trim();
    if (!user) {
      toast.error("You must be logged in to add a comment.");
      return;
    }
    if (!commentText) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setInquiryCommentLoading(prev => ({ ...prev, [inquiryId]: true }));
    const { error } = await supabase.from("client_inquiry_comments").insert({
      inquiry_id: inquiryId,
      user_id: user.id,
      comment_text: commentText,
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Inquiry comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewInquiryCommentText(prev => ({ ...prev, [inquiryId]: "" }));
    }
    setInquiryCommentLoading(prev => ({ ...prev, [inquiryId]: false }));
  };

  const handleAddIncidentComment = async (incidentId: string) => {
    const commentText = newIncidentCommentText[incidentId]?.trim();
    if (!user) {
      toast.error("You must be logged in to add a comment.");
      return;
    }
    if (!commentText) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setIncidentCommentLoading(prev => ({ ...prev, [incidentId]: true }));
    const { error } = await supabase.from("incident_comments").insert({
      incident_id: incidentId,
      user_id: user.id,
      comment_text: commentText,
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Incident comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewIncidentCommentText(prev => ({ ...prev, [incidentId]: "" }));
    }
    setIncidentCommentLoading(prev => ({ ...prev, [incidentId]: false }));
  };

  if (authLoading || orgLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Client Security Overview</h1>
        <Card>
          <CardContent>
            <p className="text-sm text-muted-foreground">Loading plan details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Client Security Overview</h1>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <AlertTriangle className="inline-block mr-2 h-5 w-5 text-red-500" />
            Security Incidents
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!canAccessFullIncidentHistory && plan?.limits.incidentHistoryDays && (
            <p className="text-sm text-orange-600 dark:text-orange-400 mb-4">
              Displaying incidents from the last {plan.limits.incidentHistoryDays} days. Upgrade your plan for full incident history.
            </p>
          )}
          {loadingIncidents ? (
            <p className="text-sm text-muted-foreground">Loading incidents...</p>
          ) : incidents.length === 0 ? (
            <p className="text-sm text-muted-foreground">No incidents reported yet.</p>
          ) : (
            <div className="grid gap-3">
              {incidents.map((incident) => (
                <div key={incident.id} className="border rounded-md p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">Incident: {incident.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                      incident.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      incident.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      incident.status === 'Resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {incident.status}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Reported: {format(new Date(incident.created_at), "PPP, hh:mm a")} by {incident.profiles?.first_name || "N/A"} {incident.profiles?.last_name || ""}
                  </p>
                  <p className="text-sm text-muted-foreground">Location: {incident.location}</p>
                  <p className="text-sm mt-2">{incident.description}</p>
                  {incident.image_url && (
                    <p className="text-sm mt-2">
                      <a href={incident.image_url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                        View Attached Image
                      </a>
                    </p>
                  )}

                  {/* Incident Comments Section */}
                  {incident.incident_comments && incident.incident_comments.length > 0 && (
                    <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <h4 className="text-md font-semibold mb-2 flex items-center">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Comments
                      </h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
                        {incident.incident_comments.map((comment) => (
                          <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                            <p className="text-xs font-medium">
                              {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({comment.profiles?.role || "N/A"})
                              </span>
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {format(new Date(comment.created_at), "PPpp")}
                            </p>
                            <p className="text-sm mt-1">{comment.comment_text}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  <form onSubmit={(e) => { e.preventDefault(); handleAddIncidentComment(incident.id); }} className="mt-4 flex gap-2">
                    <Textarea
                      placeholder="Add a new comment..."
                      value={newIncidentCommentText[incident.id] || ""}
                      onChange={(e) => setNewIncidentCommentText(prev => ({ ...prev, [incident.id]: e.target.value }))}
                      rows={2}
                      className="flex-1"
                      disabled={incidentCommentLoading[incident.id]}
                    />
                    <Button type="submit" disabled={incidentCommentLoading[incident.id]}>
                      {incidentCommentLoading[incident.id] ? "Adding..." : "Add Comment"}
                    </Button>
                  </form>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageCircleQuestion className="inline-block mr-2 h-5 w-5 text-purple-500" />
            My Inquiries
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loadingInquiries ? (
            <p className="text-sm text-muted-foreground">Loading inquiries...</p>
          ) : inquiries.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have not submitted any inquiries yet.</p>
          ) : (
            <div className="grid gap-3">
              {inquiries.map((inquiry) => (
                <div key={inquiry.id} className="border rounded-md p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium">Subject: {inquiry.subject}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                      inquiry.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      inquiry.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      inquiry.status === 'Resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {inquiry.status}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Submitted: {format(new Date(inquiry.created_at), "PPP, hh:mm a")}
                  </p>
                  <p className="text-sm mt-2">{inquiry.message}</p>

                  {/* Inquiry Comments Section */}
                  {inquiry.client_inquiry_comments && inquiry.client_inquiry_comments.length > 0 && (
                    <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <h4 className="text-md font-semibold mb-2 flex items-center">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Comments
                      </h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
                        {inquiry.client_inquiry_comments.map((comment) => (
                          <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                            <p className="text-xs font-medium">
                              {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({comment.profiles?.role || "N/A"})
                              </span>
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {format(new Date(comment.created_at), "PPpp")}
                            </p>
                            <p className="text-sm mt-1">{comment.comment_text}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  <form onSubmit={(e) => { e.preventDefault(); handleAddInquiryComment(inquiry.id); }} className="mt-4 flex gap-2">
                    <Textarea
                      placeholder="Add a new comment..."
                      value={newInquiryCommentText[inquiry.id] || ""}
                      onChange={(e) => setNewInquiryCommentText(prev => ({ ...prev, [inquiry.id]: e.target.value }))}
                      rows={2}
                      className="flex-1"
                      disabled={inquiryCommentLoading[inquiry.id]}
                    />
                    <Button type="submit" disabled={inquiryCommentLoading[inquiry.id]}>
                      {inquiryCommentLoading[inquiry.id] ? "Adding..." : "Add Comment"}
                    </Button>
                  </form>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageSquare className="inline-block mr-2 h-5 w-5 text-blue-500" />
            Recent Communications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loadingCommunications ? (
            <p className="text-sm text-muted-foreground">Loading communications...</p>
          ) : communications.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent communications.</p>
          ) : (
            <div className="grid gap-3">
              {communications.map((comm) => (
                <div key={comm.id} className="border rounded-md p-4">
                  <h3 className="font-medium">{comm.title}</h3>
                  <p className="text-sm text-muted-foreground">Received: {format(new Date(comm.created_at), "PPP, hh:mm a")}</p>
                  {/* Render HTML content */}
                  <div className="text-sm mt-2" dangerouslySetInnerHTML={{ __html: comm.content }} />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientIncidentsAndComms;