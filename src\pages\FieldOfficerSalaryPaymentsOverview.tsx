import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { Banknote, DollarSign } from "lucide-react";
import { format } from "date-fns";

interface SalaryPayment {
  id: string;
  user_id: string;
  amount: number;
  payment_date: string;
  status: string;
  cashfree_payout_id: string | null;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

const FieldOfficerSalaryPaymentsOverview = () => {
  const [payments, setPayments] = useState<SalaryPayment[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchSalaryPayments = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from("salary_payments")
      .select(`
          *,
          profiles (
            first_name,
            last_name
          )
        `)
      .order("created_at", { ascending: false });

    if (error) {
      toast.error("Failed to load salary payments: " + error.message);
      console.error("Error fetching salary payments:", error);
    } else {
      setPayments(data as SalaryPayment[]);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchSalaryPayments();

    // Set up real-time subscription for salary_payments
    const channel = supabase
      .channel('salary_payments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'salary_payments' },
        (payload) => {
          console.log('Salary payment change received!', payload);
          fetchSalaryPayments(); // Re-fetch data on any change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Salary Payments Overview</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <Banknote className="inline-block mr-2 h-5 w-5 text-green-500" />
            All Payouts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading payments...</p>
          ) : payments.length === 0 ? (
            <p className="text-sm text-muted-foreground">No salary payments recorded yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Payment Date</TableHead>
                    <TableHead>Guard</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Payout ID</TableHead>
                    <TableHead>Recorded At</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">
                        {format(new Date(payment.payment_date), "PPP")}
                      </TableCell>
                      <TableCell>
                        {payment.profiles?.first_name || "N/A"} {payment.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell>₹{payment.amount.toFixed(2)}</TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          payment.status === 'Pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          payment.status === 'Processing' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          payment.status === 'Paid' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {payment.status}
                        </span>
                      </TableCell>
                      <TableCell className="max-w-[150px] truncate">{payment.cashfree_payout_id || "N/A"}</TableCell>
                      <TableCell>{format(new Date(payment.created_at), "PPpp")}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerSalaryPaymentsOverview;