import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { PlusCircle, Edit, Trash, Package } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  created_at: string;
}

const FieldOfficerManageServices = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddEditDialogOpen, setIsAddEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentService, setCurrentService] = useState<Service | null>(null);

  // State for add/edit form
  const [serviceName, setServiceName] = useState("");
  const [serviceDescription, setServiceDescription] = useState("");
  const [servicePrice, setServicePrice] = useState("");
  const [formLoading, setFormLoading] = useState(false);

  const fetchServices = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from("services")
      .select("*")
      .order("name", { ascending: true });

    if (error) {
      toast.error("Failed to load services: " + error.message);
      console.error("Error fetching services:", error);
    } else {
      setServices(data as Service[]);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchServices();

    // Set up real-time subscription for services
    const channel = supabase
      .channel('services_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'services' },
        (payload) => {
          console.log('Service change received!', payload);
          fetchServices(); // Re-fetch data on any change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const handleAddClick = () => {
    setCurrentService(null); // Clear current service for add mode
    setServiceName("");
    setServiceDescription("");
    setServicePrice("");
    setIsAddEditDialogOpen(true);
  };

  const handleEditClick = (service: Service) => {
    setCurrentService(service);
    setServiceName(service.name);
    setServiceDescription(service.description || "");
    setServicePrice(service.price.toString());
    setIsAddEditDialogOpen(true);
  };

  const handleDeleteClick = (service: Service) => {
    setCurrentService(service);
    setIsDeleteDialogOpen(true);
  };

  const handleSaveService = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormLoading(true);

    if (!serviceName || !servicePrice) {
      toast.error("Service name and price are required.");
      setFormLoading(false);
      return;
    }

    const parsedPrice = parseFloat(servicePrice);
    if (isNaN(parsedPrice) || parsedPrice <= 0) {
      toast.error("Please enter a valid positive number for price.");
      setFormLoading(false);
      return;
    }

    if (currentService) {
      // Update existing service
      const { error } = await supabase
        .from("services")
        .update({
          name: serviceName,
          description: serviceDescription || null,
          price: parsedPrice,
        })
        .eq("id", currentService.id);

      if (error) {
        toast.error("Failed to update service: " + error.message);
        console.error("Service update error:", error);
      } else {
        toast.success("Service updated successfully!");
        setIsAddEditDialogOpen(false);
        fetchServices();
      }
    } else {
      // Add new service
      const { error } = await supabase
        .from("services")
        .insert({
          name: serviceName,
          description: serviceDescription || null,
          price: parsedPrice,
        });

      if (error) {
        toast.error("Failed to add service: " + error.message);
        console.error("Service add error:", error);
      } else {
        toast.success("Service added successfully!");
        setIsAddEditDialogOpen(false);
        fetchServices();
      }
    }
    setFormLoading(false);
  };

  const confirmDeleteService = async () => {
    if (!currentService) return;

    setFormLoading(true); // Use formLoading for delete as well
    const { error } = await supabase
      .from("services")
      .delete()
      .eq("id", currentService.id);

    if (error) {
      toast.error("Failed to delete service: " + error.message);
      console.error("Service deletion error:", error);
    } else {
      toast.success("Service deleted successfully!");
      setIsDeleteDialogOpen(false);
      fetchServices();
    }
    setFormLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Manage Services</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <Package className="inline-block mr-2 h-5 w-5 text-green-500" />
            Company Services
          </CardTitle>
          <Button onClick={handleAddClick}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Add New Service
          </Button>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading services...</p>
          ) : services.length === 0 ? (
            <p className="text-sm text-muted-foreground">No services defined yet. Click "Add New Service" to get started.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Created At</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {services.map((service) => (
                    <TableRow key={service.id}>
                      <TableCell className="font-medium">{service.name}</TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {service.description || "N/A"}
                      </TableCell>
                      <TableCell>₹{service.price.toFixed(2)}</TableCell>
                      <TableCell>{format(new Date(service.created_at), "PPpp")}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(service)} className="mr-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(service)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Service Dialog */}
      <Dialog open={isAddEditDialogOpen} onOpenChange={setIsAddEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{currentService ? "Edit Service" : "Add New Service"}</DialogTitle>
            <DialogDescription>
              {currentService ? "Make changes to the service details." : "Add a new service to your offerings."}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSaveService} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="serviceName">Service Name</Label>
              <Input
                id="serviceName"
                placeholder="e.g., Patrol Services, Alarm Monitoring"
                value={serviceName}
                onChange={(e) => setServiceName(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="serviceDescription">Description (Optional)</Label>
              <Textarea
                id="serviceDescription"
                placeholder="Provide a brief description of the service..."
                value={serviceDescription}
                onChange={(e) => setServiceDescription(e.target.value)}
                rows={3}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="servicePrice">Price (INR)</Label>
              <Input
                id="servicePrice"
                type="number"
                step="0.01"
                placeholder="e.g., 5000.00"
                value={servicePrice}
                onChange={(e) => setServicePrice(e.target.value)}
                required
              />
            </div>
            <DialogFooter>
              <Button type="submit" disabled={formLoading}>
                {formLoading ? "Saving..." : "Save Service"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Service Alert Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the service{" "}
              <span className="font-semibold">
                "{currentService ? currentService.name : ""}"
              </span>.
              Deleting a service will NOT automatically delete associated invoices.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteService} disabled={formLoading}>
              {formLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default FieldOfficerManageServices;