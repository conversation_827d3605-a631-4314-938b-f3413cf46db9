import { Link } from "react-router-dom";

const UnauthorizedPage = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 p-4">
      <div className="text-center">
        <h1 className="text-5xl font-extrabold mb-4 text-red-600 dark:text-red-400">
          Access Denied
        </h1>
        <p className="text-xl text-gray-700 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
          You do not have the necessary permissions to view this page.
        </p>
        <Link to="/" className="text-blue-600 hover:underline dark:text-blue-400">
          Go to Home Page
        </Link>
      </div>
    </div>
  );
};

export default UnauthorizedPage;