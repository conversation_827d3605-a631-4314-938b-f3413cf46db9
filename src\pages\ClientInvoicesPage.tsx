import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { DollarSign, ReceiptText } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";

interface Invoice {
  id: string;
  client_id: string;
  service_id: string;
  amount: number;
  status: string;
  due_date: string | null;
  created_at: string;
  updated_at: string;
  services: {
    name: string;
    description: string | null;
  } | null;
}

const ClientInvoicesPage = () => {
  const { user, loading: authLoading } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [paymentLoading, setPaymentLoading] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const fetchInvoices = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      setLoading(true);
      const { data, error } = await supabase
        .from("invoices")
        .select(`
          *,
          services (
            name,
            description
          )
        `)
        .eq("client_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        toast.error("Failed to load your invoices: " + error.message);
        console.error("Error fetching client invoices:", error);
        setInvoices([]);
      } else {
        setInvoices(data as Invoice[]);
      }
      setLoading(false);
    };

    if (!authLoading) {
      fetchInvoices();
    }

    // Set up real-time subscription for invoices
    const channel = supabase
      .channel('client_invoices_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'invoices' },
        (payload) => {
          if (payload.new.client_id === user?.id || payload.old.client_id === user?.id) {
            console.log('Invoice change received!', payload);
            fetchInvoices(); // Re-fetch data on any change relevant to the user
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, authLoading]);

  const handlePayNow = async (invoice: Invoice) => {
    if (!user || !user.email) {
      toast.error("User email not available for payment. Please log in again.");
      return;
    }

    setPaymentLoading(prev => ({ ...prev, [invoice.id]: true }));

    try {
      // Generate a unique order ID (e.g., using invoice ID and timestamp)
      const orderId = `INV-${invoice.id}-${Date.now()}`;

      // Invoke the Edge Function to create a Cashfree order
      const { data, error } = await supabase.functions.invoke('create-cashfree-order', {
        body: {
          amount: invoice.amount,
          currency: 'INR', // Assuming INR, adjust as needed
          order_id: orderId,
          customer_details: {
            customer_id: user.id,
            customer_email: user.email,
            customer_phone: user.phone || '9999999999', // Provide a dummy phone if not available
            customer_name: `${user.user_metadata.first_name || ''} ${user.user_metadata.last_name || ''}`.trim() || 'Client',
          },
          order_meta: {
            return_url: `${window.location.origin}/client/invoices?payment_status={payment_status}&order_id={order_id}&payment_id={payment_id}`,
            notify_url: 'YOUR_WEBHOOK_URL_HERE', // IMPORTANT: Replace with your actual webhook URL for server-side status updates
          },
        },
      });

      if (error) {
        toast.error("Failed to initiate payment: " + error.message);
        console.error("Cashfree order creation error:", error);
        return;
      }

      if (data && data.payment_link) {
        // Record the payment attempt in your database with 'created' status
        const { error: paymentInsertError } = await supabase.from("payments").insert({
          invoice_id: invoice.id,
          cashfree_order_id: orderId,
          amount: invoice.amount,
          currency: 'INR',
          status: 'created',
        });

        if (paymentInsertError) {
          console.error("Failed to record payment attempt:", paymentInsertError.message);
          toast.warning("Payment initiated, but failed to record locally. Please contact support if payment status is not updated.");
        } else {
          toast.success("Redirecting to payment gateway...");
        }
        
        // Redirect to Cashfree payment link
        window.location.href = data.payment_link;
      } else {
        toast.error("Payment link not received from Cashfree.");
      }
    } catch (error: any) {
      toast.error("An unexpected error occurred: " + error.message);
      console.error("Unexpected payment initiation error:", error);
    } finally {
      setPaymentLoading(prev => ({ ...prev, [invoice.id]: false }));
    }
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Invoices</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <ReceiptText className="inline-block mr-2 h-5 w-5 text-indigo-500" />
            Your Bills
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading invoices...</p>
          ) : invoices.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have no invoices at the moment.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Issued On</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoices.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell className="font-medium">{invoice.services?.name || "N/A"}</TableCell>
                      <TableCell>₹{invoice.amount.toFixed(2)}</TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          invoice.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          invoice.status === 'paid' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {invoice.status}
                        </span>
                      </TableCell>
                      <TableCell>{invoice.due_date ? format(new Date(invoice.due_date), "PPP") : "N/A"}</TableCell>
                      <TableCell>{format(new Date(invoice.created_at), "PPpp")}</TableCell>
                      <TableCell className="text-right">
                        {invoice.status === "pending" && (
                          <Button
                            size="sm"
                            onClick={() => handlePayNow(invoice)}
                            disabled={paymentLoading[invoice.id]}
                          >
                            <DollarSign className="mr-2 h-4 w-4" />
                            {paymentLoading[invoice.id] ? "Processing..." : "Pay Now"}
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientInvoicesPage;