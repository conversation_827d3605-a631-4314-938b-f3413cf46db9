import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  console.error("VITE_SUPABASE_URL is not defined in environment variables.");
  throw new Error("VITE_SUPABASE_URL is not defined in environment variables.");
}
if (!supabaseAnonKey) {
  console.error("VITE_SUPABASE_ANON_KEY is not defined in environment variables.");
  throw new Error("VITE_SUPABASE_ANON_KEY is not defined in environment variables.");
}

console.log("Supabase URL:", supabaseUrl ? "Loaded" : "Not Loaded");
console.log("Supabase Anon Key:", supabaseAnonKey ? "Loaded" : "Not Loaded");
console.log("Full Supabase URL:", supabaseUrl);

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Test Supabase connection
export const testSupabaseConnection = async () => {
  try {
    console.log("Testing Supabase connection...");
    const { data, error } = await supabase.auth.getSession();
    if (error) {
      console.error("Supabase connection test failed:", error);
      return false;
    }
    console.log("Supabase connection test successful");
    return true;
  } catch (error) {
    console.error("Supabase connection test error:", error);
    return false;
  }
};