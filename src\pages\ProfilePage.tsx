import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { User as UserIcon, KeyRound, Image as ImageIcon, XCircle, Banknote } from "lucide-react"; // Added Banknote icon

interface GuardBankDetails {
  id: string;
  user_id: string;
  account_number: string;
  ifsc_code: string;
  bank_name: string;
  account_holder_name: string;
}

const ProfilePage = () => {
  const { user, profile, loading: authLoading } = useAuth();
  const [firstName, setFirstName] = useState(profile?.first_name || "");
  const [lastName, setLastName] = useState(profile?.last_name || "");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loadingProfileUpdate, setLoadingProfileUpdate] = useState(false);
  const [loadingPasswordUpdate, setLoadingPasswordUpdate] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarUrlPreview, setAvatarUrlPreview] = useState<string | null>(null);
  const [currentAvatarUrl, setCurrentAvatarUrl] = useState<string | null>(profile?.avatar_url || null);

  // State for bank details
  const [bankDetails, setBankDetails] = useState<GuardBankDetails | null>(null);
  const [accountNumber, setAccountNumber] = useState("");
  const [ifscCode, setIfscCode] = useState("");
  const [bankName, setBankName] = useState("");
  const [accountHolderName, setAccountHolderName] = useState("");
  const [loadingBankDetails, setLoadingBankDetails] = useState(false);

  useEffect(() => {
    if (profile) {
      setFirstName(profile.first_name || "");
      setLastName(profile.last_name || "");
      setCurrentAvatarUrl(profile.avatar_url || null);
    }
  }, [profile]);

  useEffect(() => {
    const fetchBankDetails = async () => {
      if (user && profile?.role === 'guard') {
        setLoadingBankDetails(true);
        const { data, error } = await supabase
          .from('guard_bank_details')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
          console.error("Error fetching bank details:", error.message);
          toast.error("Failed to load bank details: " + error.message);
        } else if (data) {
          setBankDetails(data);
          setAccountNumber(data.account_number);
          setIfscCode(data.ifsc_code);
          setBankName(data.bank_name);
          setAccountHolderName(data.account_holder_name);
        }
        setLoadingBankDetails(false);
      }
    };

    if (!authLoading) {
      fetchBankDetails();
    }
  }, [user, profile, authLoading]);

  const handleAvatarFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAvatarFile(file);
      setAvatarUrlPreview(URL.createObjectURL(file));
    } else {
      setAvatarFile(null);
      setAvatarUrlPreview(null);
    }
  };

  const handleRemoveAvatar = async () => {
    if (!user) {
      toast.error("You must be logged in to remove your avatar.");
      return;
    }

    setLoadingProfileUpdate(true);
    if (currentAvatarUrl) {
      const pathSegments = currentAvatarUrl.split("avatars/");
      if (pathSegments.length > 1) {
        const filePathInStorage = pathSegments[1];
        const { error: storageError } = await supabase.storage
          .from("avatars")
          .remove([filePathInStorage]);

        if (storageError) {
          toast.error("Failed to delete old avatar from storage: " + storageError.message);
          console.error("Storage deletion error:", storageError);
          setLoadingProfileUpdate(false);
          return;
        }
      }
    }

    const { error: updateError } = await supabase
      .from("profiles")
      .update({ avatar_url: null })
      .eq("id", user.id);

    if (updateError) {
      toast.error("Failed to remove avatar from profile: " + updateError.message);
      console.error("Avatar removal error:", updateError);
    } else {
      toast.success("Avatar removed successfully!");
      setCurrentAvatarUrl(null);
      setAvatarFile(null);
      setAvatarUrlPreview(null);
      // In a real app, you might want to re-fetch profile in AuthContext here
    }
    setLoadingProfileUpdate(false);
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoadingProfileUpdate(true);

    if (!user) {
      toast.error("You must be logged in to update your profile.");
      setLoadingProfileUpdate(false);
      return;
    }

    let newAvatarUrl = currentAvatarUrl;

    if (avatarFile) {
      const fileExt = avatarFile.name.split(".").pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      // Delete old avatar if it exists
      if (currentAvatarUrl) {
        const pathSegments = currentAvatarUrl.split("avatars/");
        if (pathSegments.length > 1) {
          const oldFilePathInStorage = pathSegments[1];
          const { error: deleteOldError } = await supabase.storage
            .from("avatars")
            .remove([oldFilePathInStorage]);
          if (deleteOldError) {
            console.error("Failed to delete old avatar:", deleteOldError.message);
            // Continue with new upload even if old delete fails
          }
        }
      }

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, avatarFile, {
          cacheControl: "3600",
          upsert: false,
        });

      if (uploadError) {
        toast.error("Failed to upload avatar: " + uploadError.message);
        setLoadingProfileUpdate(false);
        return;
      }

      const { data: publicUrlData } = supabase.storage
        .from("avatars")
        .getPublicUrl(filePath);
      
      newAvatarUrl = publicUrlData.publicUrl;
    } else if (avatarUrlPreview === null && currentAvatarUrl !== null) {
      // If preview is cleared and there was a current avatar, it means user wants to remove it
      newAvatarUrl = null;
    }

    const { error } = await supabase
      .from("profiles")
      .update({ first_name: firstName, last_name: lastName, avatar_url: newAvatarUrl })
      .eq("id", user.id);

    if (error) {
      toast.error("Failed to update profile: " + error.message);
      console.error("Profile update error:", error);
    } else {
      toast.success("Profile updated successfully!");
      setCurrentAvatarUrl(newAvatarUrl);
      setAvatarFile(null);
      setAvatarUrlPreview(null);
      // Clear file input visually
      const fileInput = document.getElementById("avatarUpload") as HTMLInputElement;
      if (fileInput) fileInput.value = "";
      // In a real app, you might want to re-fetch profile in AuthContext here
    }
    setLoadingProfileUpdate(false);
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoadingPasswordUpdate(true);

    if (!user) {
      toast.error("You must be logged in to change your password.");
      setLoadingPasswordUpdate(false);
      return;
    }

    if (newPassword.length < 6) {
      toast.error("Password must be at least 6 characters long.");
      setLoadingPasswordUpdate(false);
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error("New password and confirm password do not match.");
      setLoadingPasswordUpdate(false);
      return;
    }

    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      toast.error("Failed to change password: " + error.message);
      console.error("Password change error:", error);
    } else {
      toast.success("Password changed successfully!");
      setNewPassword("");
      setConfirmPassword("");
    }
    setLoadingPasswordUpdate(false);
  };

  const handleUpdateBankDetails = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoadingBankDetails(true);

    if (!user) {
      toast.error("You must be logged in to update bank details.");
      setLoadingBankDetails(false);
      return;
    }

    if (!accountNumber || !ifscCode || !bankName || !accountHolderName) {
      toast.error("All bank details fields are required.");
      setLoadingBankDetails(false);
      return;
    }

    const bankDetailsData = {
      user_id: user.id,
      account_number: accountNumber,
      ifsc_code: ifscCode,
      bank_name: bankName,
      account_holder_name: accountHolderName,
    };

    let error;
    if (bankDetails) {
      // Update existing details
      const { error: updateError } = await supabase
        .from("guard_bank_details")
        .update(bankDetailsData)
        .eq("id", bankDetails.id);
      error = updateError;
    } else {
      // Insert new details
      const { error: insertError } = await supabase
        .from("guard_bank_details")
        .insert(bankDetailsData);
      error = insertError;
    }

    if (error) {
      toast.error("Failed to save bank details: " + error.message);
      console.error("Bank details save error:", error);
    } else {
      toast.success("Bank details saved successfully!");
      // Re-fetch to ensure state is updated, especially if it was an insert
      const { data: updatedData, error: fetchError } = await supabase
        .from('guard_bank_details')
        .select('*')
        .eq('user_id', user.id)
        .single();
      if (fetchError) {
        console.error("Error re-fetching bank details after save:", fetchError.message);
      } else {
        setBankDetails(updatedData);
      }
    }
    setLoadingBankDetails(false);
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-xl text-muted-foreground">Loading profile...</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-xl text-muted-foreground">Please log in to view your profile.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">My Profile</h1>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold flex items-center">
            <UserIcon className="mr-2 h-5 w-5 text-gray-500" />
            Personal Information
          </CardTitle>
          <CardDescription>Manage your account details.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpdateProfile} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" value={user.email || ""} disabled className="bg-gray-100 dark:bg-gray-800" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="role">Role</Label>
              <Input id="role" value={profile?.role || "N/A"} disabled className="capitalize bg-gray-100 dark:bg-gray-800" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                placeholder="Your first name"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                placeholder="Your last name"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="avatarUpload">Profile Avatar</Label>
              <Input
                id="avatarUpload"
                type="file"
                accept="image/*"
                onChange={handleAvatarFileChange}
              />
              {(avatarUrlPreview || currentAvatarUrl) && (
                <div className="mt-2 flex items-center space-x-4">
                  <img 
                    src={avatarUrlPreview || currentAvatarUrl || ""} 
                    alt="Avatar Preview" 
                    className="w-24 h-24 rounded-full object-cover border"
                  />
                  <Button variant="ghost" size="sm" onClick={handleRemoveAvatar} disabled={loadingProfileUpdate}>
                    <XCircle className="mr-2 h-4 w-4" /> Remove Avatar
                  </Button>
                </div>
              )}
              {!avatarUrlPreview && !currentAvatarUrl && (
                <div className="w-24 h-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <ImageIcon className="h-12 w-12" />
                </div>
              )}
            </div>
            <Button type="submit" className="w-full" disabled={loadingProfileUpdate}>
              {loadingProfileUpdate ? "Saving..." : "Update Profile"}
            </Button>
          </form>
        </CardContent>
      </Card>

      {profile?.role === 'guard' && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-semibold flex items-center">
              <Banknote className="mr-2 h-5 w-5 text-green-500" />
              Bank Details
            </CardTitle>
            <CardDescription>Manage your bank account for salary payouts.</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleUpdateBankDetails} className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="accountNumber">Account Number</Label>
                <Input
                  id="accountNumber"
                  placeholder="Enter your bank account number"
                  value={accountNumber}
                  onChange={(e) => setAccountNumber(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="ifscCode">IFSC Code</Label>
                <Input
                  id="ifscCode"
                  placeholder="Enter your bank's IFSC code"
                  value={ifscCode}
                  onChange={(e) => setIfscCode(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="bankName">Bank Name</Label>
                <Input
                  id="bankName"
                  placeholder="e.g., State Bank of India"
                  value={bankName}
                  onChange={(e) => setBankName(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="accountHolderName">Account Holder Name</Label>
                <Input
                  id="accountHolderName"
                  placeholder="Name as per bank records"
                  value={accountHolderName}
                  onChange={(e) => setAccountHolderName(e.target.value)}
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loadingBankDetails}>
                {loadingBankDetails ? "Saving..." : "Save Bank Details"}
              </Button>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold flex items-center">
            <KeyRound className="mr-2 h-5 w-5 text-gray-500" />
            Change Password
          </CardTitle>
          <CardDescription>Update your account password.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleChangePassword} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="newPassword">New Password</Label>
              <Input
                id="newPassword"
                type="password"
                required
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
            <Button type="submit" className="w-full" disabled={loadingPasswordUpdate}>
              {loadingPasswordUpdate ? "Changing..." : "Change Password"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfilePage;