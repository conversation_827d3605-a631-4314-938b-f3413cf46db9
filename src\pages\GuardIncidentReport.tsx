import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import React, { useState } from "react";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";

const GuardIncidentReport = () => {
  const { user } = useAuth();
  const [incidentTitle, setIncidentTitle] = useState("");
  const [incidentDescription, setIncidentDescription] = useState("");
  const [incidentLocation, setIncidentLocation] = useState("");
  const [incidentImage, setIncidentImage] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setIncidentImage(e.target.files[0]);
    } else {
      setIncidentImage(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!user) {
      toast.error("You must be logged in to report an incident.");
      setLoading(false);
      return;
    }

    if (!incidentTitle || !incidentDescription || !incidentLocation) {
      toast.error("Please fill in all required fields.");
      setLoading(false);
      return;
    }

    let imageUrl: string | null = null;
    let latitude: number | null = null;
    let longitude: number | null = null;

    // Get current GPS location
    if (navigator.geolocation) {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          });
        });
        latitude = position.coords.latitude;
        longitude = position.coords.longitude;
        toast.info("Location captured for incident report.");
      } catch (geoError: any) {
        console.error("Geolocation error:", geoError);
        if (geoError.code === geoError.PERMISSION_DENIED) {
          toast.warning("Location access denied. Incident reported without precise location.");
        } else if (geoError.code === geoError.POSITION_UNAVAILABLE) {
          toast.warning("Location information unavailable. Incident reported without precise location.");
        } else if (geoError.code === geoError.TIMEOUT) {
          toast.warning("Location request timed out. Incident reported without precise location.");
        } else {
          toast.error("Failed to get location: " + geoError.message);
        }
      }
    } else {
      toast.warning("Geolocation is not supported by your browser. Incident reported without precise location.");
    }

    if (incidentImage) {
      const fileExt = incidentImage.name.split(".").pop();
      const fileName = `${user.id}/${Date.now()}.${fileExt}`;
      const filePath = `incidents/${fileName}`;

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("incident_media")
        .upload(filePath, incidentImage, {
          cacheControl: "3600",
          upsert: false,
        });

      if (uploadError) {
        toast.error("Failed to upload image: " + uploadError.message);
        setLoading(false);
        return;
      }

      const { data: publicUrlData } = supabase.storage
        .from("incident_media")
        .getPublicUrl(filePath);
      
      imageUrl = publicUrlData.publicUrl;
    }

    const { error: insertError } = await supabase.from("incidents").insert({
      user_id: user.id,
      title: incidentTitle,
      description: incidentDescription,
      location: incidentLocation,
      image_url: imageUrl,
      status: "Open", // Set initial status
      latitude: latitude, // Add latitude
      longitude: longitude, // Add longitude
    });

    if (insertError) {
      toast.error("Failed to submit incident report: " + insertError.message);
      console.error("Incident submission error:", insertError);
    } else {
      toast.success("Incident report submitted successfully!");
      setIncidentTitle("");
      setIncidentDescription("");
      setIncidentLocation("");
      setIncidentImage(null);
      // Clear file input visually
      const fileInput = document.getElementById("incidentImage") as HTMLInputElement;
      if (fileInput) fileInput.value = "";
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Report Incident</h1>
      <Card>
        <CardHeader>
          <CardTitle>New Incident Report</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Incident Title</Label>
              <Input
                id="title"
                placeholder="e.g., Suspicious Activity, Broken Window"
                value={incidentTitle}
                onChange={(e) => setIncidentTitle(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Provide a detailed description of the incident..."
                value={incidentDescription}
                onChange={(e) => setIncidentDescription(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                placeholder="e.g., Main Gate, Sector C, Building 5"
                value={incidentLocation}
                onChange={(e) => setIncidentLocation(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="incidentImage">Attach Image (Optional)</Label>
              <Input
                id="incidentImage"
                type="file"
                accept="image/*"
                onChange={handleFileChange}
              />
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Submitting..." : "Submit Report"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default GuardIncidentReport;