import { useAuth } from "@/hooks/useAuth"; // Corrected import path
import { PricingPlan } from "@/lib/pricingPlans";

interface OrganizationDetails {
  id: string;
  name: string;
  plan_type: string;
  current_guards_count: number;
  current_clients_count: number;
  current_shifts_this_month: number;
  current_timesheets_this_month: number;
  last_monthly_reset_date: string;
}

interface UseOrganizationResult {
  organization: OrganizationDetails | null;
  plan: PricingPlan | undefined;
  loading: boolean;
  isFreePlan: boolean;
  isStarterPlan: boolean;
  isGrowthPlan: boolean;
  isEnterprisePlan: boolean;
  canAddGuard: boolean;
  canAddClient: boolean;
  canScheduleShift: boolean;
  canSubmitTimesheet: boolean;
  canAccessLiveTracking: boolean;
  canAccessReportsAnalytics: boolean;
  canCreateInvoice: boolean;
  hasBranding: boolean; // true means no branding, false means "Powered by MirazSec"
  canAccessFullIncidentHistory: boolean;
  canCreateMoreGuards: boolean; // Convenience alias
  canCreateMoreClients: boolean; // Convenience alias
  canScheduleMoreShifts: boolean; // Convenience alias
  canSubmitMoreTimesheets: boolean; // Convenience alias
  canAccessAiInsights: boolean; // New: AI insights access
}

export const useOrganization = (): UseOrganizationResult => {
  const { organization, plan, loading } = useAuth();

  const isFreePlan = plan?.id === 'free';
  const isStarterPlan = plan?.id === 'starter';
  const isGrowthPlan = plan?.id === 'growth';
  const isEnterprisePlan = plan?.id === 'enterprise';

  // Feature/Limit checks
  const canAddGuard = !organization || plan?.limits.guards === 'unlimited' || (organization.current_guards_count < (plan?.limits.guards || 0));
  const canAddClient = !organization || plan?.limits.clients === 'unlimited' || (organization.current_clients_count < (plan?.limits.clients || 0));
  const canScheduleShift = !organization || plan?.limits.shiftSchedulingPerMonth === 'unlimited' || (organization.current_shifts_this_month < (plan?.limits.shiftSchedulingPerMonth || 0));
  const canSubmitTimesheet = !organization || plan?.limits.timesheetSubmissionsPerMonth === 'unlimited' || (organization.current_timesheets_this_month < (plan?.limits.timesheetSubmissionsPerMonth || 0));
  const canAccessLiveTracking = plan?.limits.liveTracking === true;
  const canAccessReportsAnalytics = plan?.limits.reportsAnalytics === true;
  const canCreateInvoice = plan?.limits.invoiceCreation === true;
  const hasBranding = plan?.limits.branding === true; // true means NO branding (white-labeled)
  const canAccessFullIncidentHistory = plan?.limits.incidentHistoryDays === 'unlimited';
  const canAccessAiInsights = plan?.limits.aiInsights === true; // New: AI insights access

  return {
    organization,
    plan,
    loading,
    isFreePlan,
    isStarterPlan,
    isGrowthPlan,
    isEnterprisePlan,
    canAddGuard,
    canAddClient,
    canScheduleShift,
    canSubmitTimesheet,
    canAccessLiveTracking,
    canAccessReportsAnalytics,
    canCreateInvoice,
    hasBranding,
    canAccessFullIncidentHistory,
    canCreateMoreGuards: canAddGuard, // Alias for clarity in some contexts
    canCreateMoreClients: canAddClient, // Alias for clarity in some contexts
    canScheduleMoreShifts: canScheduleShift, // Alias for clarity in some contexts
    canSubmitMoreTimesheets: canSubmitTimesheet, // Alias for clarity in some contexts
    canAccessAiInsights, // New: AI insights access
  };
};