import { describe, it, expect } from 'vitest';
import { cn } from './utils';

describe('cn', () => {
  it('should combine class names correctly', () => {
    expect(cn('class1', 'class2')).toBe('class1 class2');
  });

  it('should handle conditional classes', () => {
    expect(cn('class1', true && 'class2', false && 'class3')).toBe('class1 class2');
  });

  it('should merge Tailwind classes without conflicts', () => {
    expect(cn('p-4', 'p-2', 'text-red-500', 'text-blue-500')).toBe('p-2 text-blue-500');
  });

  it('should handle empty inputs', () => {
    expect(cn('', null, undefined, 'class1')).toBe('class1');
  });

  it('should return an empty string if no valid classes are provided', () => {
    expect(cn('', null, undefined)).toBe('');
  });
});