"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { MessageSquarePlus, CalendarIcon } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; // Import Quill styles

const FieldOfficerScheduleCommunication = () => {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [scheduledAt, setScheduledAt] = useState<Date | undefined>(undefined);
  const [scheduledTime, setScheduledTime] = useState("");
  const [targetRoles, setTargetRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const availableRoles = [
    { value: "guard", label: "Guards" },
    { value: "client", label: "Clients" },
    { value: "field_officer", label: "Field Officers" },
  ];

  const handleRoleChange = (role: string) => {
    setTargetRoles(prev =>
      prev.includes(role) ? prev.filter(r => r !== role) : [...prev, role]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!title || !content || content === "<p><br></p>" || !scheduledAt || !scheduledTime || targetRoles.length === 0) {
      toast.error("Please fill in all required fields and select at least one target role.");
      setLoading(false);
      return;
    }

    const [hours, minutes] = scheduledTime.split(':').map(Number);
    const fullScheduledDateTime = new Date(scheduledAt);
    fullScheduledDateTime.setHours(hours, minutes, 0, 0);

    if (fullScheduledDateTime < new Date()) {
      toast.error("Scheduled time cannot be in the past.");
      setLoading(false);
      return;
    }

    const { error } = await supabase.from("scheduled_communications").insert({
      title,
      content,
      scheduled_at: fullScheduledDateTime.toISOString(),
      target_roles: targetRoles,
      status: "pending",
    });

    if (error) {
      toast.error("Failed to schedule communication: " + error.message);
      console.error("Scheduled communication submission error:", error);
    } else {
      toast.success("Communication scheduled successfully!");
      setTitle("");
      setContent("");
      setScheduledAt(undefined);
      setScheduledTime("");
      setTargetRoles([]);
      navigate("/field-officer/communications-overview"); // Redirect to communications overview
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Schedule New Communication</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquarePlus className="mr-2 h-5 w-5 text-blue-500" />
            Schedule a Future Message
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                placeholder="e.g., Upcoming Drill, Policy Update"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="content">Content</Label>
              <ReactQuill
                theme="snow"
                value={content}
                onChange={setContent}
                placeholder="Write your detailed message here..."
                className="min-h-[150px]"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="scheduledDate">Scheduled Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !scheduledAt && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {scheduledAt ? format(scheduledAt, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={scheduledAt}
                      onSelect={setScheduledAt}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="scheduledTime">Scheduled Time</Label>
                <Input
                  id="scheduledTime"
                  type="time"
                  required
                  value={scheduledTime}
                  onChange={(e) => setScheduledTime(e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label>Target Roles</Label>
              <div className="flex flex-wrap gap-2">
                {availableRoles.map((role) => (
                  <Button
                    key={role.value}
                    type="button"
                    variant={targetRoles.includes(role.value) ? "default" : "outline"}
                    onClick={() => handleRoleChange(role.value)}
                  >
                    {role.label}
                  </Button>
                ))}
              </div>
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Scheduling..." : "Schedule Communication"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerScheduleCommunication;