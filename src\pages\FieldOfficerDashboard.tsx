import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import React, { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { AlertCircle, MessageCircleQuestion, BellRing, ListTodo, CalendarDays, MessageSquareText, MessageCircle } from "lucide-react";
import { isToday, isThisWeek, parseISO, format } from "date-fns";
import { Link, useNavigate } from "react-router-dom"; // Import useNavigate
import { Button } from "@/components/ui/button";

interface Shift {
  id: string;
  shift_date: string;
  start_time: string;
  end_time: string;
}

interface Incident {
  id: string;
  title: string;
  description: string;
  created_at: string;
  status: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

interface Communication {
  id: string;
  title: string;
  content: string;
  created_at: string;
}

const FieldOfficerDashboard = () => {
  const navigate = useNavigate(); // Initialize useNavigate
  const [guardsMonitored, setGuardsMonitored] = useState<number | null>(null);
  const [loadingGuards, setLoadingGuards] = useState(true);
  const [resolvedIncidentsCount, setResolvedIncidentsCount] = useState<number | null>(null);
  const [loadingResolvedIncidents, setLoadingResolvedIncidents] = useState(true);
  const [openInquiriesCount, setOpenInquiriesCount] = useState<number | null>(null);
  const [loadingOpenInquiries, setLoadingOpenInquiries] = useState(true);
  const [activePanicAlertsCount, setActivePanicAlertsCount] = useState<number | null>(null);
  const [loadingActivePanicAlerts, setLoadingActivePanicAlerts] = useState(true);
  const [pendingTasksCount, setPendingTasksCount] = useState<number | null>(null);
  const [loadingPendingTasks, setLoadingPendingTasks] = useState(true);
  const [todayShiftsCount, setTodayShiftsCount] = useState<number | null>(null);
  const [thisWeekShiftsCount, setThisWeekShiftsCount] = useState<number | null>(null);
  const [loadingScheduledShifts, setLoadingScheduledShifts] = useState(true);
  const [recentIncidents, setRecentIncidents] = useState<Incident[]>([]);
  const [loadingRecentIncidents, setLoadingRecentIncidents] = useState(true);
  const [recentCommunications, setRecentCommunications] = useState<Communication[]>([]);
  const [loadingRecentCommunications, setLoadingRecentCommunications] = useState(true);
  const [openSupportChatsCount, setOpenSupportChatsCount] = useState<number | null>(null);
  const [loadingSupportChats, setLoadingSupportChats] = useState(true);

  const fetchGuardsCount = async () => {
    setLoadingGuards(true);
    const { count, error } = await supabase
      .from("profiles")
      .select("*", { count: "exact", head: true })
      .eq("role", "guard");
    if (error) {
      console.error("Error fetching guards count:", error);
      setGuardsMonitored(0);
    } else {
      setGuardsMonitored(count);
    }
    setLoadingGuards(false);
  };

  const fetchResolvedIncidentsCount = async () => {
    setLoadingResolvedIncidents(true);
    const { count, error } = await supabase
      .from("incidents")
      .select("*", { count: "exact", head: true })
      .eq("status", "Resolved");
    if (error) {
      console.error("Error fetching resolved incidents count:", error);
      setResolvedIncidentsCount(0);
    } else {
      setResolvedIncidentsCount(count);
    }
    setLoadingResolvedIncidents(false);
  };

  const fetchOpenInquiriesCount = async () => {
    setLoadingOpenInquiries(true);
    const { count, error } = await supabase
      .from("client_inquiries")
      .select("*", { count: "exact", head: true })
      .eq("status", "Open");
    if (error) {
      console.error("Error fetching open inquiries count:", error);
      setOpenInquiriesCount(0);
    } else {
      setOpenInquiriesCount(count);
    }
    setLoadingOpenInquiries(false);
  };

  const fetchActivePanicAlertsCount = async () => {
    setLoadingActivePanicAlerts(true);
    const { count, error } = await supabase
      .from("panic_alerts")
      .select("*", { count: "exact", head: true })
      .eq("status", "Active");
    if (error) {
      console.error("Error fetching active panic alerts count:", error);
      setActivePanicAlertsCount(0);
    } else {
      setActivePanicAlertsCount(count);
    }
    setLoadingActivePanicAlerts(false);
  };

  const fetchPendingTasksCount = async () => {
    setLoadingPendingTasks(true);
    const { count, error } = await supabase
      .from("tasks")
      .select("*", { count: "exact", head: true })
      .in("status", ["Pending", "In Progress"]);
    if (error) {
      console.error("Error fetching pending tasks count:", error);
      setPendingTasksCount(0);
    } else {
      setPendingTasksCount(count);
    }
    setLoadingPendingTasks(false);
  };

  const fetchScheduledShiftsCount = async () => {
    setLoadingScheduledShifts(true);
    const { data: shiftsData, error } = await supabase
      .from("shifts")
      .select("shift_date");
    if (error) {
      console.error("Error fetching scheduled shifts:", error);
      setTodayShiftsCount(0);
      setThisWeekShiftsCount(0);
    } else {
      const today = new Date();
      let todayCount = 0;
      let thisWeekCount = 0;
      (shiftsData as Shift[]).forEach(shift => {
        const shiftDate = parseISO(shift.shift_date);
        if (isToday(shiftDate)) {
          todayCount++;
        }
        if (isThisWeek(shiftDate, { weekStartsOn: 1 })) {
          thisWeekCount++;
        }
      });
      setTodayShiftsCount(todayCount);
      setThisWeekShiftsCount(thisWeekCount);
    }
    setLoadingScheduledShifts(false);
  };

  const fetchRecentIncidents = async () => {
    setLoadingRecentIncidents(true);
    const { data, error } = await supabase
      .from("incidents")
      .select(`
        *,
        profiles (
          first_name,
          last_name
        )
      `)
      .order("created_at", { ascending: false })
      .limit(5);

    if (error) {
      toast.error("Failed to load recent incidents: " + error.message);
      console.error("Error fetching recent incidents:", error);
      setRecentIncidents([]);
    } else {
      setRecentIncidents(data as Incident[]);
    }
    setLoadingRecentIncidents(false);
  };

  const fetchRecentCommunications = async () => {
    setLoadingRecentCommunications(true);
    const { data, error } = await supabase
      .from("communications")
      .select("*")
      .order("created_at", { ascending: false })
      .limit(5);

    if (error) {
      toast.error("Failed to load recent communications: " + error.message);
      console.error("Error fetching recent communications:", error);
      setRecentCommunications([]);
    } else {
      setRecentCommunications(data as Communication[]);
    }
    setLoadingRecentCommunications(false);
  };

  const fetchOpenSupportChatsCount = async () => {
    setLoadingSupportChats(true);
    const { count, error } = await supabase
      .from("support_chats")
      .select("*", { count: "exact", head: true })
      .in("status", ["Open", "In Progress"]);
    if (error) {
      console.error("Error fetching open support chats count:", error);
      setOpenSupportChatsCount(0);
    } else {
      setOpenSupportChatsCount(count);
    }
    setLoadingSupportChats(false);
  };

  useEffect(() => {
    fetchGuardsCount();
    const profilesChannel = supabase
      .channel('dashboard_profiles_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'profiles' }, () => {
        fetchGuardsCount();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(profilesChannel);
    };
  }, []);

  useEffect(() => {
    fetchResolvedIncidentsCount();
    fetchRecentIncidents();
    const incidentsChannel = supabase
      .channel('dashboard_incidents_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'incidents' }, () => {
        fetchResolvedIncidentsCount();
        fetchRecentIncidents();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(incidentsChannel);
    };
  }, []);

  useEffect(() => {
    fetchOpenInquiriesCount();
    const inquiriesChannel = supabase
      .channel('dashboard_client_inquiries_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'client_inquiries' }, () => {
        fetchOpenInquiriesCount();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(inquiriesChannel);
    };
  }, []);

  useEffect(() => {
    fetchActivePanicAlertsCount();
    const panicAlertsChannel = supabase
      .channel('dashboard_panic_alerts_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'panic_alerts' }, async (payload) => {
        console.log('Panic alert change received!', payload);
        fetchActivePanicAlertsCount(); // Re-fetch count

        if (payload.eventType === 'INSERT') {
          const newAlert = payload.new;
          const { data: guardProfile, error: profileError } = await supabase
            .from('profiles')
            .select('first_name, last_name')
            .eq('id', newAlert.user_id)
            .single();

          const guardName = guardProfile ? `${guardProfile.first_name || ''} ${guardProfile.last_name || ''}`.trim() : 'Unknown Guard';
          const alertTime = format(new Date(newAlert.created_at), 'hh:mm a');

          toast.error(`🚨 New Panic Alert from ${guardName} at ${alertTime}!`, {
            action: {
              label: "View Alert",
              onClick: () => navigate("/field-officer/panic-alerts"),
            },
            duration: 10000, // Keep toast visible for 10 seconds
          });
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(panicAlertsChannel);
    };
  }, [navigate]); // Add navigate to dependency array

  useEffect(() => {
    fetchPendingTasksCount();
    const tasksChannel = supabase
      .channel('dashboard_tasks_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'tasks' }, () => {
        fetchPendingTasksCount();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(tasksChannel);
    };
  }, []);

  useEffect(() => {
    fetchScheduledShiftsCount();
    const shiftsChannel = supabase
      .channel('dashboard_shifts_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'shifts' }, () => {
        fetchScheduledShiftsCount();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(shiftsChannel);
    };
  }, []);

  useEffect(() => {
    fetchRecentCommunications();
    const communicationsChannel = supabase
      .channel('dashboard_communications_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'communications' }, () => {
        fetchRecentCommunications();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(communicationsChannel);
    };
  }, []);

  useEffect(() => {
    fetchOpenSupportChatsCount();
    const supportChatsChannel = supabase
      .channel('dashboard_support_chats_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'support_chats' }, () => {
        fetchOpenSupportChatsCount();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(supportChatsChannel);
    };
  }, []);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Field Officer Dashboard</h1>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Guards Monitored
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingGuards ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{guardsMonitored}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Total registered guards
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Resolved Incidents
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingResolvedIncidents ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{resolvedIncidentsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Incidents marked as resolved
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Open Client Inquiries
            </CardTitle>
            <MessageCircleQuestion className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingOpenInquiries ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{openInquiriesCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Client inquiries awaiting action
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Panic Alerts
            </CardTitle>
            <BellRing className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingActivePanicAlerts ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{activePanicAlertsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Urgent alerts from guards
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Tasks
            </CardTitle>
            <ListTodo className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingPendingTasks ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{pendingTasksCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Assigned tasks awaiting completion
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Scheduled Shifts
            </CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingScheduledShifts ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{todayShiftsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              {todayShiftsCount !== null && thisWeekShiftsCount !== null
                ? `${todayShiftsCount} today, ${thisWeekShiftsCount} this week`
                : "No shifts scheduled"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Open Support Chats
            </CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingSupportChats ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{openSupportChatsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Active conversations with users
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="mr-2 h-5 w-5 text-red-500" />
            Recent Incidents
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loadingRecentIncidents ? (
            <p className="text-sm text-muted-foreground">Loading recent incidents...</p>
          ) : recentIncidents.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent incidents to display.</p>
          ) : (
            <div className="space-y-3">
              {recentIncidents.map((incident) => (
                <div key={incident.id} className="border rounded-md p-3">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="font-medium text-sm">{incident.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                      incident.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      incident.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      incident.status === 'Resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {incident.status}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Reported by {incident.profiles?.first_name || "N/A"} {incident.profiles?.last_name || ""} on {format(new Date(incident.created_at), "PPP")}
                  </p>
                  <p className="text-xs mt-1 truncate">{incident.description}</p>
                </div>
              ))}
              <Button variant="link" className="p-0 h-auto" asChild>
                <Link to="/field-officer/incidents">View All Incidents</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquareText className="mr-2 h-5 w-5 text-blue-500" />
            Recent Communications
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loadingRecentCommunications ? (
            <p className="text-sm text-muted-foreground">Loading recent communications...</p>
          ) : recentCommunications.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent communications to display.</p>
          ) : (
            <div className="space-y-3">
              {recentCommunications.map((comm) => (
                <div key={comm.id} className="border rounded-md p-3">
                  <h3 className="font-medium text-sm">{comm.title}</h3>
                  <p className="text-xs text-muted-foreground">
                    Sent: {format(new Date(comm.created_at), "PPP, hh:mm a")}
                  </p>
                  <p className="text-xs mt-1 truncate">{comm.content}</p>
                </div>
              ))}
              <Button variant="link" className="p-0 h-auto" asChild>
                <Link to="/field-officer/communications-overview">View All Communications</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerDashboard;