import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { MessageSquareText } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { format } from "date-fns";
import { useAuth } from "@/context/AuthContext"; // Import useAuth

interface Communication {
  id: string;
  title: string;
  content: string; // Content is now HTML
  created_at: string;
  target_roles: string[]; // Added target_roles
}

const GuardCommunicationsOverview = () => {
  const { user, loading: authLoading, profile } = useAuth(); // Get profile
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCommunications = async () => {
      if (!profile?.role) { // Wait for profile to load
        setLoading(false);
        return;
      }

      setLoading(true);
      const { data, error } = await supabase
        .from("communications")
        .select("*")
        .contains("target_roles", [profile.role]) // Filter by user's role
        .order("created_at", { ascending: false });

      if (error) {
        toast.error("Failed to load communications: " + error.message);
        console.error("Error fetching communications for guards:", error);
      } else {
        setCommunications(data as Communication[]);
      }
      setLoading(false);
    };

    if (!authLoading) { // Only fetch once auth is loaded
      fetchCommunications();
    }
  }, [profile, authLoading]); // Depend on profile and authLoading

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Communications</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageSquareText className="inline-block mr-2 h-5 w-5 text-blue-500" />
            Recent Announcements
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading communications...</p>
          ) : communications.length === 0 ? (
            <p className="text-sm text-muted-foreground">No recent communications or announcements.</p>
          ) : (
            <div className="grid gap-3">
              {communications.map((comm) => (
                <div key={comm.id} className="border rounded-md p-4">
                  <h3 className="font-medium">{comm.title}</h3>
                  <p className="text-sm text-muted-foreground">Received: {format(new Date(comm.created_at), "PPP, hh:mm a")}</p>
                  {/* Render HTML content */}
                  <div className="text-sm mt-2" dangerouslySetInnerHTML={{ __html: comm.content }} />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GuardCommunicationsOverview;