export interface PricingPlan {
  id: string;
  name: string;
  monthlyPrice: number | 'Custom';
  annualPrice: number | 'Custom';
  features: string[];
  limits: {
    guards: number | 'unlimited';
    clients: number | 'unlimited';
    shiftSchedulingPerMonth: number | 'unlimited';
    timesheetSubmissionsPerMonth: number | 'unlimited';
    incidentHistoryDays: number | 'unlimited';
    liveTracking: boolean;
    reportsAnalytics: boolean;
    invoiceCreation: boolean;
    branding: boolean; // true means no branding, false means "Powered by MirazSec"
    aiInsights: boolean; // New: AI insights access
  };
}

export const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'free',
    name: 'Free Plan',
    monthlyPrice: 0,
    annualPrice: 0,
    features: [
      'Basic security management',
      'Limited incident history',
      'Standard support',
    ],
    limits: {
      guards: 3,
      clients: 1,
      shiftSchedulingPerMonth: 10,
      timesheetSubmissionsPerMonth: 10,
      incidentHistoryDays: 30,
      liveTracking: false,
      reportsAnalytics: false,
      invoiceCreation: false,
      branding: false, // "Powered by MirazSec" branding present
      aiInsights: false,
    },
  },
  {
    id: 'starter',
    name: 'Starter Plan',
    monthlyPrice: 999,
    annualPrice: 9990, // 2 months free
    features: [
      'Up to 20 guards',
      'Unlimited clients & incidents',
      'Full reports & analytics',
      'Priority support',
    ],
    limits: {
      guards: 20,
      clients: 'unlimited',
      shiftSchedulingPerMonth: 'unlimited',
      timesheetSubmissionsPerMonth: 'unlimited',
      incidentHistoryDays: 'unlimited',
      liveTracking: true,
      reportsAnalytics: true,
      invoiceCreation: true,
      branding: true, // No "Powered by MirazSec" branding
      aiInsights: true,
    },
  },
  {
    id: 'growth',
    name: 'Growth Plan',
    monthlyPrice: 2999,
    annualPrice: 29990, // 2 months free
    features: [
      'Up to 100 guards',
      'Premium reports & analytics',
      'Mobile-first dashboard',
      'Dedicated support',
    ],
    limits: {
      guards: 100,
      clients: 'unlimited',
      shiftSchedulingPerMonth: 'unlimited',
      timesheetSubmissionsPerMonth: 'unlimited',
      incidentHistoryDays: 'unlimited',
      liveTracking: true,
      reportsAnalytics: true,
      invoiceCreation: true,
      branding: true, // No "Powered by MirazSec" branding
      aiInsights: true,
    },
  },
  {
    id: 'enterprise',
    name: 'Enterprise Plan',
    monthlyPrice: 'Custom',
    annualPrice: 'Custom',
    features: [
      'Unlimited guards & clients',
      'Dedicated support team',
      'White-labeled application',
      'Custom integrations',
    ],
    limits: {
      guards: 'unlimited',
      clients: 'unlimited',
      shiftSchedulingPerMonth: 'unlimited',
      timesheetSubmissionsPerMonth: 'unlimited',
      incidentHistoryDays: 'unlimited',
      liveTracking: true,
      reportsAnalytics: true,
      invoiceCreation: true,
      branding: true, // No "Powered by MirazSec" branding
      aiInsights: true,
    },
  },
];

export const getPlanDetails = (planType: string): PricingPlan | undefined => {
  return PRICING_PLANS.find(plan => plan.id === planType);
};