import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CheckCircle, XCircle } from "lucide-react";
import { PRICING_PLANS } from "@/lib/pricingPlans";
import { Button } from "@/components/ui/button"; // Import Button component
import { Link } from "react-router-dom"; // Import Link for navigation

const PricingPage = () => {
  const featuresAndLimits = [
    { key: 'monthlyPrice', label: 'Monthly Price', type: 'price' },
    { key: 'annualPrice', label: 'Annual Price', type: 'price' },
    { key: 'guards', label: 'Max Guards', type: 'limit' },
    { key: 'clients', label: 'Max Clients', type: 'limit' },
    { key: 'shiftSchedulingPerMonth', label: 'Shift Scheduling / Month', type: 'limit' },
    { key: 'timesheetSubmissionsPer<PERSON>ont<PERSON>', label: 'Timesheet Submissions / Month', type: 'limit' },
    { key: 'incidentHistoryDays', label: 'Incident History (Days)', type: 'limit' },
    { key: 'liveTracking', label: 'Live Tracking', type: 'boolean' },
    { key: 'reportsAnalytics', label: 'Reports & Analytics', type: 'boolean' },
    { key: 'invoiceCreation', label: 'Invoice Creation', type: 'boolean' },
    { key: 'branding', label: 'MirazSec Branding', type: 'branding' }, // Special type for branding
    { key: 'aiInsights', label: 'AI Insights', type: 'boolean' },
  ];

  const saasVsLicenseFeatures = [
    { label: '💻 Deployment', saas: 'Cloud-hosted on MirazSec.com', license: 'Deployed on your own server/cloud' },
    { label: '🔒 Data Ownership', saas: 'MirazSec manages and stores', license: 'You own and host your data' },
    { label: '⚙️ Features Included', saas: 'All core features', license: 'All core features' },
    { label: '🧠 AI-Powered Insights', saas: 'Included', license: 'Included' },
    { label: '🌐 Custom Domain & Branding', saas: 'Optional Add-on', license: 'Fully customizable' },
    { label: '📍 Real-time Location Tracking', saas: 'Included', license: 'Included' },
    { label: '📅 Shift Scheduling, Timesheets, Tasks', saas: 'Included', license: 'Included' },
    { label: '📑 Incident Reporting & Panic Alerts', saas: 'Included', license: 'Included' },
    { label: '💬 Messaging & Support Chat', saas: 'Included', license: 'Included' },
    { label: '📊 Reports & Analytics', saas: 'Included', license: 'Included' },
    { label: '🧾 Invoicing + Payout Module', saas: 'Included', license: 'Included' },
    { label: '🔧 Maintenance & Updates', saas: 'Included', license: 'Optional (paid support)' },
    { label: '☎️ Technical Support', saas: 'Email/Chat Support', license: '3-month basic support included' },
    { label: '📱 Mobile App Integration', saas: 'Optional Add-on', license: 'Optional Add-on' },
    { label: '🔑 Access Limit (Guards/Clients)', saas: 'Unlimited', license: 'Unlimited' },
    { label: '💰 Price', saas: '₹1,00,000/year', license: '₹1,50,000 one-time (avg)' },
  ];

  return (
    <div className="space-y-6 p-4 md:p-8">
      <h1 className="text-4xl font-bold text-center mb-8">
        🔐 MirazSec Subscription Plans – For Security Agencies
      </h1>

      <Card className="w-full overflow-x-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-center">
            Compare Our Plans
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px] text-lg font-bold">Feature / Plan</TableHead>
                {PRICING_PLANS.map(plan => (
                  <TableHead key={plan.id} className="text-center text-lg font-bold">
                    {plan.name}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {featuresAndLimits.map(feature => (
                <TableRow key={feature.key}>
                  <TableCell className="font-medium">{feature.label}</TableCell>
                  {PRICING_PLANS.map(plan => (
                    <TableCell key={`${plan.id}-${feature.key}`} className="text-center">
                      {feature.type === 'price' && (
                        plan[feature.key as keyof typeof plan] === 'Custom'
                          ? 'Custom'
                          : `₹${(plan[feature.key as keyof typeof plan] as number).toLocaleString('en-IN')}`
                      )}
                      {feature.type === 'limit' && (
                        plan.limits[feature.key as keyof typeof plan.limits] === 'unlimited'
                          ? 'Unlimited'
                          : plan.limits[feature.key as keyof typeof plan.limits]
                      )}
                      {feature.type === 'boolean' && (
                        plan.limits[feature.key as keyof typeof plan.limits] ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500 mx-auto" />
                        )
                      )}
                      {feature.type === 'branding' && (
                        plan.limits.branding ? ( // true means NO branding
                          <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500 mx-auto" />
                        )
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
              <TableRow className="bg-gray-50 dark:bg-gray-800">
                <TableCell className="font-bold text-lg">Key Features</TableCell>
                {PRICING_PLANS.map(plan => (
                  <TableCell key={`${plan.id}-features`} className="text-left align-top">
                    <ul className="list-disc list-inside text-sm text-muted-foreground">
                      {plan.features.map((feat, idx) => (
                        <li key={idx}>{feat}</li>
                      ))}
                    </ul>
                  </TableCell>
                ))}
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <h2 className="text-3xl font-bold text-center mt-12 mb-8">
        Deployment Options: SaaS vs. Full Code License
      </h2>

      <Card className="w-full overflow-x-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-center">
            Choose Your Deployment Model
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px] text-lg font-bold">Feature</TableHead>
                <TableHead className="text-center text-lg font-bold">🟦 SaaS Plan (Annual)</TableHead>
                <TableHead className="text-center text-lg font-bold">🟥 Full Code License (One-Time)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {saasVsLicenseFeatures.map((feature, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{feature.label}</TableCell>
                  <TableCell className="text-center">{feature.saas}</TableCell>
                  <TableCell className="text-center">{feature.license}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card className="w-full mt-8 p-6 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
        <CardHeader>
          <CardTitle className="text-2xl font-semibold text-center text-blue-700 dark:text-blue-300">
            ✅ Special Offers:
          </CardTitle>
        </CardHeader>
        <CardContent className="text-lg text-center text-gray-700 dark:text-gray-300 space-y-3">
          <p>
            <strong>Early Bird SaaS Clients</strong> – Get 20% off for the first year
          </p>
          <p>
            <strong>Agencies with 100+ Guards</strong> – Eligible for custom pricing and mobile app bundle
          </p>
          <p>
            <strong>Sales Partners</strong> – Earn ₹10,000+ per client referred or closed
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default PricingPage;