import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseAdminClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '' // Use service role key for admin access
    );

    const now = new Date().toISOString();

    // 1. Fetch pending scheduled communications that are due
    const { data: scheduledComms, error: fetchError } = await supabaseAdminClient
      .from('scheduled_communications')
      .select('*')
      .eq('status', 'pending')
      .lte('scheduled_at', now);

    if (fetchError) {
      console.error('Error fetching scheduled communications:', fetchError.message);
      return new Response(JSON.stringify({ error: 'Failed to fetch scheduled communications' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    if (scheduledComms.length === 0) {
      return new Response(JSON.stringify({ message: 'No scheduled communications due.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      });
    }

    console.log(`Processing ${scheduledComms.length} scheduled communications.`);

    const results = await Promise.all(scheduledComms.map(async (comm) => {
      try {
        // 2. Insert into the main communications table, including target_roles
        const { error: insertError } = await supabaseAdminClient
          .from('communications')
          .insert({
            title: comm.title,
            content: comm.content,
            target_roles: comm.target_roles, // Now including target_roles
          });

        if (insertError) {
          console.error(`Failed to insert communication ${comm.id}:`, insertError.message);
          await supabaseAdminClient
            .from('scheduled_communications')
            .update({ status: 'failed' })
            .eq('id', comm.id);
          return { id: comm.id, status: 'failed', error: insertError.message };
        }

        // 3. Update status in scheduled_communications to 'sent'
        await supabaseAdminClient
          .from('scheduled_communications')
          .update({ status: 'sent' })
          .eq('id', comm.id);

        console.log(`Successfully sent communication: ${comm.title}`);
        return { id: comm.id, status: 'sent' };
      } catch (mapError) {
        console.error(`Unexpected error processing communication ${comm.id}:`, mapError);
        await supabaseAdminClient
          .from('scheduled_communications')
          .update({ status: 'failed' })
          .eq('id', comm.id);
        return { id: comm.id, status: 'failed', error: (mapError as Error).message };
      }
    }));

    return new Response(JSON.stringify({ message: 'Scheduled communications processed.', results }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error in schedule-communications function:', error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});