import { MadeWithDyad } from "@/components/made-with-dyad";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ShieldCheck, AlertTriangle, CalendarDays, MessageSquareText, BarChart2, Users, MessageCircleQuestion, MapPin } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-950 text-gray-900 dark:text-gray-100">
      {/* Hero Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 text-center bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-950">
        <div className="container px-4 md:px-6">
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 text-primary dark:text-primary-foreground leading-tight">
            MirazSec: Your Complete Security Operations Platform
          </h1>
          <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-10 max-w-3xl mx-auto">
            Streamline incident reporting, manage shifts, track guards, and enhance communication for a safer, more efficient security operation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg">
              <Link to="/signup">Get Started - Sign Up</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-gray-800 shadow-lg">
              <Link to="/login">Login to Your Portal</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-white dark:bg-gray-900">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold mb-4 text-primary dark:text-primary-foreground">
              Key Features Designed for You
            </h2>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto">
              MirazSec offers a robust suite of tools to empower every role in your security team.
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">Incident Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Report incidents instantly with location and images. Field officers get a centralized overview for quick response and resolution.
                </p>
              </CardContent>
            </Card>

            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <CalendarDays className="h-12 w-12 text-green-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">Shift & Task Scheduling</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Efficiently schedule guard shifts and assign tasks. Guards can view their schedules and update task statuses.
                </p>
              </CardContent>
            </Card>

            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <MapPin className="h-12 w-12 text-blue-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">Live Guard Tracking</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Monitor guard locations in real-time on an interactive map for enhanced safety and operational awareness.
                </p>
              </CardContent>
            </Card>

            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <MessageSquareText className="h-12 w-12 text-purple-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">Seamless Communication</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Send announcements, manage client inquiries, and utilize dedicated support chats for all users.
                </p>
              </CardContent>
            </Card>

            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <BarChart2 className="h-12 w-12 text-orange-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">Reports & Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Gain valuable insights with comprehensive reports on incidents, timesheets, and overall operations.
                </p>
              </CardContent>
            </Card>

            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <Users className="h-12 w-12 text-indigo-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">User & Role Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Manage user accounts and assign roles (Guard, Client, Field Officer, Admin) with tailored permissions.
                </p>
              </CardContent>
            </Card>

            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <MessageCircleQuestion className="h-12 w-12 text-cyan-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">Client Inquiry System</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Clients can easily submit inquiries, and field officers can track and respond to them efficiently.
                </p>
              </CardContent>
            </Card>

            <Card className="flex flex-col items-center text-center p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <ShieldCheck className="h-12 w-12 text-gray-500 mb-4" />
              <CardHeader>
                <CardTitle className="text-xl font-semibold">Secure & Scalable</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Built on a robust and secure platform with role-based access control and real-time data synchronization.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action / Role Selection Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-t from-blue-50 to-white dark:from-gray-900 dark:to-gray-950">
        <div className="container px-4 md:px-6 text-center">
          <h2 className="text-3xl sm:text-4xl font-bold mb-4 text-primary dark:text-primary-foreground">
            Ready to Enhance Your Security Operations?
          </h2>
          <p className="text-lg text-gray-700 dark:text-gray-300 mb-10 max-w-2xl mx-auto">
            Select your role to explore the features tailored for your needs or sign up to get started today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg">
              <Link to="/guard">Guard Portal</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-gray-800 shadow-lg">
              <Link to="/client">Client Portal</Link>
            </Button>
            <Button asChild size="lg" variant="secondary" className="bg-green-600 hover:bg-green-700 text-white shadow-lg">
              <Link to="/field-officer">Field Officer Portal</Link>
            </Button>
          </div>
        </div>
      </section>

      <div className="absolute bottom-4">
        <MadeWithDyad />
      </div>
    </div>
  );
};

export default Index;