import { MadeWithDyad } from "@/components/made-with-dyad";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

const Index = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      <div className="text-center p-4">
        <h1 className="text-5xl font-extrabold mb-6 text-primary dark:text-primary-foreground">
          Welcome to MirazSec
        </h1>
        <p className="text-xl text-gray-700 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
          Your comprehensive security management platform. Select your role to get started.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
            <Link to="/guard">Guard Portal</Link>
          </Button>
          <Button asChild size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-gray-800">
            <Link to="/client">Client Portal</Link>
          </Button>
          <Button asChild size="lg" variant="secondary" className="bg-green-600 hover:bg-green-700 text-white">
            <Link to="/field-officer">Field Officer Portal</Link>
          </Button>
        </div>
      </div>
      <div className="absolute bottom-4">
        <MadeWithDyad />
      </div>
    </div>
  );
};

export default Index;