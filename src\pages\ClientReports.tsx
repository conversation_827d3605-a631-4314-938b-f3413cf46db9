"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { BarChart2, MessageCircleQuestion, AlertTriangle } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";
import IncidentStatusChart from "@/components/IncidentStatusChart"; // Reusing for inquiry status too

interface StatusData {
  name: string;
  value: number;
}

const ClientReports: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [incidentCount, setIncidentCount] = useState<number | null>(null);
  const [inquiryCount, setInquiryCount] = useState<number | null>(null);
  const [incidentStatusData, setIncidentStatusData] = useState<StatusData[]>([]);
  const [inquiryStatusData, setInquiryStatusData] = useState<StatusData[]>([]);

  useEffect(() => {
    const fetchReportsData = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      setLoading(true);
      let hasError = false;

      // Fetch Incident Count and Status
      const { data: incidentsData, count: incidentsTotal, error: incidentsError } = await supabase
        .from("incidents")
        .select("status", { count: "exact" })
        .eq("user_id", user.id);

      if (incidentsError) {
        toast.error("Failed to load incident data: " + incidentsError.message);
        console.error("Error fetching client incidents for reports:", incidentsError);
        setIncidentCount(0);
        setIncidentStatusData([]);
        hasError = true;
      } else {
        setIncidentCount(incidentsTotal);
        const statusCounts: { [key: string]: number } = {};
        incidentsData.forEach((incident: { status: string }) => {
          statusCounts[incident.status] = (statusCounts[incident.status] || 0) + 1;
        });
        const formattedData = Object.keys(statusCounts).map(status => ({
          name: status,
          value: statusCounts[status],
        }));
        setIncidentStatusData(formattedData);
      }

      // Fetch Inquiry Count and Status
      const { data: inquiriesData, count: inquiriesTotal, error: inquiriesError } = await supabase
        .from("client_inquiries")
        .select("status", { count: "exact" })
        .eq("client_id", user.id);

      if (inquiriesError) {
        toast.error("Failed to load inquiry data: " + inquiriesError.message);
        console.error("Error fetching client inquiries for reports:", inquiriesError);
        setInquiryCount(0);
        setInquiryStatusData([]);
        hasError = true;
      } else {
        setInquiryCount(inquiriesTotal);
        const statusCounts: { [key: string]: number } = {};
        inquiriesData.forEach((inquiry: { status: string }) => {
          statusCounts[inquiry.status] = (statusCounts[inquiry.status] || 0) + 1;
        });
        const formattedData = Object.keys(statusCounts).map(status => ({
          name: status,
          value: statusCounts[status],
        }));
        setInquiryStatusData(formattedData);
      }

      setLoading(false);
      if (hasError) {
        toast.info("Some report data could not be loaded.");
      }
    };

    if (!authLoading) {
      fetchReportsData();
    }

    // Set up real-time subscriptions for relevant tables
    const incidentsChannel = supabase
      .channel('client_reports_incidents_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incidents' },
        (payload) => {
          if (payload.new.user_id === user?.id || payload.old.user_id === user?.id) {
            fetchReportsData();
          }
        }
      )
      .subscribe();

    const inquiriesChannel = supabase
      .channel('client_reports_inquiries_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'client_inquiries' },
        (payload) => {
          if (payload.new.client_id === user?.id || payload.old.client_id === user?.id) {
            fetchReportsData();
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(incidentsChannel);
      supabase.removeChannel(inquiriesChannel);
    };
  }, [user, authLoading]);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Reports & Analytics</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <BarChart2 className="inline-block mr-2 h-5 w-5 text-orange-500" />
            Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            This section provides an overview of your security activities and inquiries.
          </p>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading report data...</p>
          ) : (
            <div className="mt-4 space-y-2">
              <div className="border rounded-md p-3 flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Total Incidents Reported by You</h4>
                  <p className="text-xs text-muted-foreground">All security incidents you have recorded.</p>
                </div>
                <span className="text-2xl font-bold text-red-600">{incidentCount}</span>
              </div>
              <div className="border rounded-md p-3 flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Total Inquiries Submitted by You</h4>
                  <p className="text-xs text-muted-foreground">All inquiries you have sent.</p>
                </div>
                <span className="text-2xl font-bold text-purple-600">{inquiryCount}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
            Your Incident Status Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading chart data...</p>
          ) : incidentStatusData.length > 0 ? (
            <IncidentStatusChart data={incidentStatusData} />
          ) : (
            <p className="text-sm text-muted-foreground">No incident data available for charting.</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center">
            <MessageCircleQuestion className="mr-2 h-5 w-5 text-purple-500" />
            Your Inquiry Status Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading chart data...</p>
          ) : inquiryStatusData.length > 0 ? (
            <IncidentStatusChart data={inquiryStatusData} />
          ) : (
            <p className="text-sm text-muted-foreground">No inquiry data available for charting.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientReports;
