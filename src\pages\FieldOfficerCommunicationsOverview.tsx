import React, { useEffect, useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { MessageSquareText, Edit, Trash } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; // Import Quill styles
import { useAuth } from "@/context/AuthContext"; // Import useAuth

interface Communication {
  id: string;
  title: string;
  content: string; // Content is now HTML
  created_at: string;
  target_roles: string[]; // Added target_roles
}

const FieldOfficerCommunicationsOverview = () => {
  const { profile, loading: authLoading } = useAuth(); // Get profile
  const [communications, setCommunications] = useState<Communication[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentCommunication, setCurrentCommunication] = useState<Communication | null>(null);

  // State for edit form
  const [editTitle, setEditTitle] = useState("");
  const [editContent, setEditContent] = useState(""); // Content will be HTML

  const fetchCommunications = async () => {
    if (!profile?.role) { // Wait for profile to load
      setLoading(false);
      return;
    }
    setLoading(true);
    const { data, error } = await supabase
      .from("communications")
      .select("*")
      .contains("target_roles", [profile.role]) // Filter by user's role
      .order("created_at", { ascending: false });

    if (error) {
      toast.error("Failed to load communications: " + error.message);
      console.error("Error fetching communications:", error);
    } else {
      setCommunications(data as Communication[]);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!authLoading) { // Only fetch once auth is loaded
      fetchCommunications();
    }

    // Set up real-time subscription for communications
    const channel = supabase
      .channel('communications_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'communications' },
        (payload) => {
          if (profile?.role && payload.new.target_roles?.includes(profile.role)) {
            console.log('Communication change received!', payload);
            fetchCommunications(); // Re-fetch data on any change
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [profile, authLoading]); // Depend on profile and authLoading

  const handleEditClick = (comm: Communication) => {
    setCurrentCommunication(comm);
    setEditTitle(comm.title);
    setEditContent(comm.content);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (comm: Communication) => {
    setCurrentCommunication(comm);
    setIsDeleteDialogOpen(true);
  };

  const handleUpdateCommunication = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCommunication) return;

    if (!editTitle || !editContent || editContent === "<p><br></p>") {
      toast.error("Please fill in both the title and content fields.");
      return;
    }

    setLoading(true);
    const { error } = await supabase
      .from("communications")
      .update({
        title: editTitle,
        content: editContent,
      })
      .eq("id", currentCommunication.id);

    if (error) {
      toast.error("Failed to update communication: " + error.message);
      console.error("Communication update error:", error);
    } else {
      toast.success("Communication updated successfully!");
      setIsEditDialogOpen(false);
      fetchCommunications(); // Re-fetch communications to show updated data
    }
    setLoading(false);
  };

  const confirmDeleteCommunication = async () => {
    if (!currentCommunication) return;

    setLoading(true);
    const { error } = await supabase
      .from("communications")
      .delete()
      .eq("id", currentCommunication.id);

    if (error) {
      toast.error("Failed to delete communication: " + error.message);
      console.error("Communication deletion error:", error);
    } else {
      toast.success("Communication deleted successfully!");
      setIsDeleteDialogOpen(false);
      fetchCommunications(); // Re-fetch communications to show updated data
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Communications Overview</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageSquareText className="inline-block mr-2 h-5 w-5 text-blue-500" />
            All Sent Communications
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading communications...</p>
          ) : communications.length === 0 ? (
            <p className="text-sm text-muted-foreground">No communications sent yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Content</TableHead>
                    <TableHead>Target Roles</TableHead> {/* New TableHead */}
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {communications.map((comm) => (
                    <TableRow key={comm.id}>
                      <TableCell className="font-medium">
                        {format(new Date(comm.created_at), "PPpp")}
                      </TableCell>
                      <TableCell>{comm.title}</TableCell>
                      <TableCell className="max-w-[400px] truncate">
                        {/* Render HTML content */}
                        <div dangerouslySetInnerHTML={{ __html: comm.content }} />
                      </TableCell>
                      <TableCell className="capitalize">
                        {comm.target_roles.join(", ")} {/* Display target roles */}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(comm)} className="mr-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(comm)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Communication Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Communication</DialogTitle>
            <DialogDescription>
              Make changes to the communication here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateCommunication} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="editTitle">Title</Label>
              <Input
                id="editTitle"
                placeholder="e.g., Important Update"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editContent">Content</Label>
              <ReactQuill
                theme="snow"
                value={editContent}
                onChange={setEditContent}
                placeholder="Write your message here..."
                className="min-h-[150px]"
              />
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Communication Alert Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the communication titled{" "}
              <span className="font-semibold">
                "{currentCommunication ? currentCommunication.title : ""}"
              </span>.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteCommunication} disabled={loading}>
              {loading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default FieldOfficerCommunicationsOverview;