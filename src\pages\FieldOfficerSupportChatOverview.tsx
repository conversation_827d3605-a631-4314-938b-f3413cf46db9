import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { MessageCircle, Edit, Send } from "lucide-react";
import { format } from "date-fns";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"; // Import Table components

interface SupportChat {
  id: string;
  user_id: string;
  subject: string;
  status: string;
  created_at: string;
  updated_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
  support_messages: SupportMessage[];
}

interface SupportMessage {
  id: string;
  chat_id: string;
  sender_id: string;
  message_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
}

const FieldOfficerSupportChatOverview = () => {
  const { user, loading: authLoading } = useAuth();
  const [chats, setChats] = useState<SupportChat[]>([]);
  const [users, setUsers] = useState<UserProfile[]>([]); // State for all users for filtering
  const [loadingChats, setLoadingChats] = useState(true);
  const [selectedChat, setSelectedChat] = useState<SupportChat | null>(null);
  const [currentMessageText, setCurrentMessageText] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);
  const [editStatus, setEditStatus] = useState("");

  // State for filters and sorting
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterSender, setFilterSender] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("updated_at");
  const [sortOrder, setSortOrder] = useState<"desc" | "asc">("desc");

  const fetchChats = async () => {
    setLoadingChats(true);
    let query = supabase
      .from("support_chats")
      .select(`
        *,
        profiles (
          first_name,
          last_name,
          role
        ),
        support_messages (
          *,
          profiles (
            first_name,
            last_name,
            role
          )
        )
      `);

    // Apply filters
    if (filterStatus !== "all") {
      query = query.eq("status", filterStatus);
    }
    if (filterSender !== "all") {
      query = query.eq("user_id", filterSender);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === "asc" });

    const { data, error } = await query;

    if (error) {
      toast.error("Failed to load support chats: " + error.message);
      console.error("Error fetching support chats:", error);
      setChats([]);
    } else {
      const chatsWithSortedMessages = (data as SupportChat[]).map(chat => ({
        ...chat,
        support_messages: chat.support_messages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      }));
      setChats(chatsWithSortedMessages);
    }
    setLoadingChats(false);
  };

  const fetchUsers = async () => {
    const { data, error } = await supabase
      .from("profiles")
      .select("id, first_name, last_name, role");

    if (error) {
      toast.error("Failed to load users for filtering: " + error.message);
      console.error("Error fetching users:", error);
    } else {
      setUsers(data || []);
    }
  };

  useEffect(() => {
    fetchChats();
  }, [filterStatus, filterSender, sortBy, sortOrder]); // Re-fetch on filter/sort change

  useEffect(() => {
    fetchUsers();

    const chatChannel = supabase
      .channel('field_officer_support_chat_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'support_chats' },
        () => {
          fetchChats();
        }
      )
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'support_messages' },
        (payload) => {
          if (selectedChat && (payload.new.chat_id === selectedChat.id || payload.old.chat_id === selectedChat.id)) {
            fetchChats(); // Re-fetch to update messages in the currently open chat
          } else if (payload.new.sender_id !== user?.id) { // Notify if a new message comes from someone else
            toast.info(`New message in chat: "${chats.find(c => c.id === payload.new.chat_id)?.subject}"`);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(chatChannel);
    };
  }, [user, selectedChat, chats]); // Added 'chats' to dependency array for toast notification logic

  const handleSendMessage = async (chatId: string) => {
    if (!user || !currentMessageText.trim()) {
      toast.error("Message cannot be empty.");
      return;
    }
    setSendingMessage(true);

    const { error } = await supabase.from("support_messages").insert({
      chat_id: chatId,
      sender_id: user.id,
      message_text: currentMessageText.trim(),
    });

    if (error) {
      toast.error("Failed to send message: " + error.message);
      console.error("Error sending message:", error);
    } else {
      setCurrentMessageText("");
      // Update chat's updated_at timestamp to bring it to top
      await supabase.from("support_chats").update({ updated_at: new Date().toISOString() }).eq("id", chatId);
      fetchChats(); // Re-fetch to update messages and chat order
    }
    setSendingMessage(false);
  };

  const handleUpdateChatStatus = async (chatId: string, newStatus: string) => {
    setLoadingChats(true); // Use general loading for status update
    const { error } = await supabase
      .from("support_chats")
      .update({ status: newStatus, updated_at: new Date().toISOString() })
      .eq("id", chatId);

    if (error) {
      toast.error("Failed to update chat status: " + error.message);
      console.error("Error updating chat status:", error);
    } else {
      toast.success("Chat status updated successfully!");
      fetchChats(); // Re-fetch to update status and chat order
      if (selectedChat && selectedChat.id === chatId) {
        setSelectedChat(prev => prev ? { ...prev, status: newStatus } : null);
      }
    }
    setLoadingChats(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Support Chat Overview</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageCircle className="inline-block mr-2 h-5 w-5 text-blue-500" />
            All Support Conversations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="filterStatus">Filter by Status</Label>
              <Select onValueChange={setFilterStatus} value={filterStatus}>
                <SelectTrigger id="filterStatus">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Open">Open</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="filterSender">Filter by Sender</Label>
              <Select onValueChange={setFilterSender} value={filterSender}>
                <SelectTrigger id="filterSender">
                  <SelectValue placeholder="All Senders" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Senders</SelectItem>
                  {users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.first_name} {user.last_name} ({user.role})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="sortBy">Sort By</Label>
              <Select onValueChange={setSortBy} value={sortBy}>
                <SelectTrigger id="sortBy">
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="updated_at">Last Updated</SelectItem>
                  <SelectItem value="created_at">Created At</SelectItem>
                  <SelectItem value="subject">Subject</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[100px]">
              <Label htmlFor="sortOrder">Order</Label>
              <Select onValueChange={setSortOrder} value={sortOrder}>
                <SelectTrigger id="sortOrder">
                  <SelectValue placeholder="Order" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {loadingChats ? (
            <p className="text-sm text-muted-foreground">Loading chats...</p>
          ) : chats.length === 0 ? (
            <p className="text-sm text-muted-foreground">No support chats found matching your criteria.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Subject</TableHead>
                    <TableHead>From</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Update</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {chats.map((chat) => (
                    <TableRow key={chat.id} onClick={() => { setSelectedChat(chat); setEditStatus(chat.status); }} className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800">
                      <TableCell className="font-medium">{chat.subject}</TableCell>
                      <TableCell>
                        {chat.profiles?.first_name || "N/A"} {chat.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                          chat.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          chat.status === 'Closed' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {chat.status}
                        </span>
                      </TableCell>
                      <TableCell>{format(new Date(chat.updated_at), "PPpp")}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); setSelectedChat(chat); setEditStatus(chat.status); }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected Chat Dialog */}
      {selectedChat && (
        <Dialog open={!!selectedChat} onOpenChange={() => setSelectedChat(null)}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
            <DialogHeader>
              <DialogTitle>Chat with {selectedChat.profiles?.first_name} {selectedChat.profiles?.last_name}</DialogTitle>
              <DialogDescription>
                Subject: {selectedChat.subject}
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto p-4 border rounded-md bg-gray-50 dark:bg-gray-800 space-y-3 mb-4">
              {selectedChat.support_messages.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center">No messages yet. Send the first one!</p>
              ) : (
                selectedChat.support_messages.map((message) => (
                  <div key={message.id} className={`flex ${message.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] p-3 rounded-lg ${
                      message.sender_id === user?.id
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100'
                    }`}>
                      <p className="text-xs font-semibold mb-1">
                        {message.profiles?.first_name || "Unknown"} {message.profiles?.last_name || ""}
                        <span className="text-xs text-muted-foreground ml-1">
                          ({message.profiles?.role || "N/A"})
                        </span>
                      </p>
                      <p className="text-sm">{message.message_text}</p>
                      <p className={`text-xs mt-1 ${message.sender_id === user?.id ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
                        {format(new Date(message.created_at), "MMM dd, hh:mm a")}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
            <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(selectedChat.id); }} className="flex gap-2">
              <Textarea
                placeholder="Type your message here..."
                value={currentMessageText}
                onChange={(e) => setCurrentMessageText(e.target.value)}
                rows={1}
                className="flex-1"
                disabled={sendingMessage || selectedChat.status === 'Closed'}
              />
              <Button type="submit" disabled={sendingMessage || selectedChat.status === 'Closed'}>
                <Send className="h-4 w-4" />
              </Button>
            </form>
            {selectedChat.status === 'Closed' && (
              <p className="text-center text-sm text-muted-foreground mt-2">This chat is closed. You cannot send new messages.</p>
            )}

            <div className="mt-4 pt-4 border-t">
              <h3 className="text-lg font-semibold mb-3 flex items-center">
                <Edit className="mr-2 h-5 w-5" />
                Update Chat Status
              </h3>
              <div className="grid gap-2">
                <Label htmlFor="chatStatus">Status</Label>
                <Select onValueChange={(value) => { setEditStatus(value); handleUpdateChatStatus(selectedChat.id, value); }} value={editStatus}>
                  <SelectTrigger id="chatStatus">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Open">Open</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default FieldOfficerSupportChatOverview;