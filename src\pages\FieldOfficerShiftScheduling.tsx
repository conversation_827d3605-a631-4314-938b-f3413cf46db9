import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { CalendarIcon, Clock, User } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useNavigate } from "react-router-dom";
import { useOrganization } from "@/hooks/useOrganization"; // Import useOrganization hook

interface GuardProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
}

const FieldOfficerShiftScheduling = () => {
  const { organization, plan, loading: orgLoading, canScheduleShift } = useOrganization(); // Use the hook
  const [guards, setGuards] = useState<GuardProfile[]>([]);
  const [selectedGuardId, setSelectedGuardId] = useState<string>("");
  const [shiftDate, setShiftDate] = useState<Date | undefined>(new Date());
  const [startTime, setStartTime] = useState("");
  const [endTime, setEndTime] = useState("");
  const [location, setLocation] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchGuards = async () => {
      const { data, error } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .eq("role", "guard");

      if (error) {
        toast.error("Failed to load guards: " + error.message);
        console.error("Error fetching guards:", error);
      } else {
        setGuards(data || []);
      }
    };
    fetchGuards();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!canScheduleShift) {
      toast.error(`You have reached your shift scheduling limit for this month (${plan?.limits.shiftSchedulingPerMonth}). Upgrade your plan to schedule more shifts.`);
      setLoading(false);
      return;
    }

    if (!selectedGuardId || !shiftDate || !startTime || !endTime || !location) {
      toast.error("Please fill in all required fields.");
      setLoading(false);
      return;
    }

    const { error } = await supabase.from("shifts").insert({
      user_id: selectedGuardId,
      shift_date: format(shiftDate, "yyyy-MM-dd"),
      start_time: startTime,
      end_time: endTime,
      location: location,
    });

    if (error) {
      toast.error("Failed to schedule shift: " + error.message);
      console.error("Shift scheduling error:", error);
    } else {
      toast.success("Shift scheduled successfully!");
      // Increment current_shifts_this_month count in organizations table
      if (organization && plan?.limits.shiftSchedulingPerMonth !== 'unlimited') {
        const { error: updateOrgError } = await supabase
          .from('organizations')
          .update({ current_shifts_this_month: organization.current_shifts_this_month + 1 })
          .eq('id', organization.id);
        if (updateOrgError) {
          console.error("Failed to update organization shift count:", updateOrgError.message);
        }
      }
      setSelectedGuardId("");
      setShiftDate(new Date());
      setStartTime("");
      setEndTime("");
      setLocation("");
      navigate("/field-officer/shift-schedule-overview"); // Redirect to a new overview page or existing one
    }
    setLoading(false);
  };

  if (orgLoading) {
    return (
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">Schedule New Shift</h1>
        <Card>
          <CardContent>
            <p className="text-sm text-muted-foreground">Loading plan details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Schedule New Shift</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2 h-5 w-5 text-blue-500" />
            Create Shift
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!canScheduleShift && organization && plan?.limits.shiftSchedulingPerMonth !== 'unlimited' ? (
            <div className="text-center p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md">
              <p className="font-semibold mb-2">Shift Scheduling Limit Reached!</p>
              <p className="text-sm">
                You have scheduled {organization.current_shifts_this_month} out of {plan?.limits.shiftSchedulingPerMonth} shifts allowed on your current <span className="capitalize">{plan?.name}</span> plan.
              </p>
              <p className="text-sm mt-2">Upgrade your plan to schedule more shifts.</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="guard">Assign Guard</Label>
                <Select onValueChange={setSelectedGuardId} value={selectedGuardId}>
                  <SelectTrigger id="guard">
                    <SelectValue placeholder="Select a guard" />
                  </SelectTrigger>
                  <SelectContent>
                    {guards.map((guard) => (
                      <SelectItem key={guard.id} value={guard.id}>
                        {guard.first_name} {guard.last_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="shiftDate">Shift Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !shiftDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {shiftDate ? format(shiftDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={shiftDate}
                      onSelect={setShiftDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="startTime">Start Time</Label>
                  <Input
                    id="startTime"
                    type="time"
                    required
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="endTime">End Time</Label>
                  <Input
                    id="endTime"
                    type="time"
                    required
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  placeholder="e.g., Main Gate, Sector C"
                  required
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? "Scheduling..." : "Schedule Shift"}
              </Button>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerShiftScheduling;