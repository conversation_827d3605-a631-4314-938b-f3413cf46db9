import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import { format, isFuture, parseISO, sub } from "date-fns"; // Added 'sub' for date calculations
import { CalendarDays, AlertCircle, MapPin, MessageCircleQuestion, BarChart2 } from "lucide-react"; // Added BarChart2 for consistency
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useAuth } from "@/context/AuthContext"; // Import useAuth

interface Communication {
  id: string;
  title: string;
  content: string;
  created_at: string;
}

interface Shift {
  id: string;
  user_id: string;
  shift_date: string;
  start_time: string;
  end_time: string;
  location: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

interface Incident {
  id: string;
  user_id: string;
  title: string;
  description: string;
  location: string;
  image_url: string | null;
  created_at: string;
  status: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

const ClientDashboard = () => {
  const { user, loading: authLoading } = useAuth(); // Get user and authLoading from AuthContext
  const [clientRecentIncidents, setClientRecentIncidents] = useState<Incident[]>([]); // Changed from count to array
  const [loadingClientIncidents, setLoadingClientIncidents] = useState(true);
  const [latestCommunication, setLatestCommunication] = useState<Communication | null>(null);
  const [loadingCommunications, setLoadingCommunications] = useState(true);
  const [upcomingShifts, setUpcomingShifts] = useState<Shift[]>([]);
  const [loadingShifts, setLoadingShifts] = useState(true);
  const [activeGuardsCount, setActiveGuardsCount] = useState<number | null>(null);
  const [loadingActiveGuards, setLoadingActiveGuards] = useState(true);
  const [openInquiriesCount, setOpenInquiriesCount] = useState<number | null>(null); // New state for open inquiries
  const [loadingOpenInquiries, setLoadingOpenInquiries] = useState(true); // New state for loading open inquiries

  useEffect(() => {
    const fetchClientIncidents = async () => { // Renamed and modified to fetch recent incidents
      if (!user) {
        setLoadingClientIncidents(false);
        return;
      }
      setLoadingClientIncidents(true);
      const { data, error } = await supabase
        .from("incidents")
        .select(`
          *,
          profiles (
            first_name,
            last_name
          )
        `)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(3); // Limit to 3 recent incidents

      if (error) {
        toast.error("Failed to fetch your recent incidents: " + error.message);
        console.error("Error fetching client recent incidents:", error);
        setClientRecentIncidents([]);
      } else {
        setClientRecentIncidents(data as Incident[]);
      }
      setLoadingClientIncidents(false);
    };

    const fetchLatestCommunication = async () => {
      setLoadingCommunications(true);
      const { data, error } = await supabase
        .from("communications")
        .select("*")
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
        toast.error("Failed to load latest communication: " + error.message);
        console.error("Error fetching latest communication:", error);
        setLatestCommunication(null);
      } else if (data) {
        setLatestCommunication(data);
      } else {
        setLatestCommunication(null);
      }
      setLoadingCommunications(false);
    };

    const fetchUpcomingShifts = async () => {
      setLoadingShifts(true);
      const { data, error } = await supabase
        .from("shifts")
        .select(`
          *,
          profiles (
            first_name,
            last_name
          )
        `)
        .order("shift_date", { ascending: true })
        .order("start_time", { ascending: true })
        .limit(3); // Limit to 3 upcoming shifts

      if (error) {
        toast.error("Failed to load shifts: " + error.message);
        console.error("Error fetching shifts for client dashboard:", error);
        setUpcomingShifts([]);
      } else {
        const now = new Date();
        const futureShifts = (data as Shift[]).filter(shift => {
          const shiftDateTime = parseISO(`${shift.shift_date}T${shift.start_time}`);
          return isFuture(shiftDateTime);
        });
        setUpcomingShifts(futureShifts);
      }
      setLoadingShifts(false);
    };

    const fetchActiveGuardsCount = async () => {
      setLoadingActiveGuards(true);
      const fiveMinutesAgo = sub(new Date(), { minutes: 5 });
      const { data: distinctLocations, error: distinctError } = await supabase
        .from("guard_locations")
        .select("user_id")
        .gt("timestamp", fiveMinutesAgo.toISOString());

      if (distinctError) {
        toast.error("Failed to fetch distinct active guards: " + distinctError.message);
        console.error("Error fetching distinct active guards:", distinctError);
        setActiveGuardsCount(0);
      } else {
        const uniqueGuardIds = new Set(distinctLocations.map(loc => loc.user_id));
        setActiveGuardsCount(uniqueGuardIds.size);
      }
      setLoadingActiveGuards(false);
    };

    const fetchOpenInquiriesCount = async () => { // New function to fetch open inquiries count
      if (!user) {
        setLoadingOpenInquiries(false);
        return;
      }
      setLoadingOpenInquiries(true);
      const { count, error } = await supabase
        .from("client_inquiries")
        .select("*", { count: "exact", head: true })
        .in("status", ["Open", "In Progress"]);

      if (error) {
        toast.error("Failed to fetch your open inquiries count: " + error.message);
        console.error("Error fetching client open inquiries count:", error);
        setOpenInquiriesCount(0);
      } else {
        setOpenInquiriesCount(count);
      }
      setLoadingOpenInquiries(false);
    };

    if (!authLoading) {
      fetchClientIncidents();
      fetchLatestCommunication();
      fetchUpcomingShifts();
      fetchActiveGuardsCount();
      fetchOpenInquiriesCount(); // Call new fetch function
    }

    // Set up real-time subscription for guard locations to update active guards count
    const channel = supabase
      .channel('guard_locations_changes_client_dashboard')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'guard_locations' },
        (payload) => {
          console.log('Guard location change received for client dashboard!', payload);
          fetchActiveGuardsCount(); // Re-fetch active guards count on any change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, authLoading]); // Depend on user and authLoading

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Client Dashboard</h1>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Guards (Last 5 min)
            </CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingActiveGuards ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{activeGuardsCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Guards with recent location updates
            </p>
            <Button variant="link" className="p-0 h-auto mt-2" asChild>
              <Link to="/client/guard-locations">View Live Map</Link>
            </Button>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              My Open Inquiries
            </CardTitle>
            <MessageCircleQuestion className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingOpenInquiries ? (
              <div className="text-2xl font-bold">Loading...</div>
            ) : (
              <div className="text-2xl font-bold">{openInquiriesCount}</div>
            )}
            <p className="text-xs text-muted-foreground">
              Inquiries awaiting response or resolution
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Upcoming Scheduled Shifts
            </CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loadingShifts ? (
              <p className="text-sm text-muted-foreground">Loading shifts...</p>
            ) : upcomingShifts.length > 0 ? (
              <div className="space-y-2">
                {upcomingShifts.map(shift => (
                  <div key={shift.id} className="border-b pb-2 last:border-b-0 last:pb-0">
                    <p className="text-sm font-medium">
                      {format(new Date(shift.shift_date), "PPP")}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {`${shift.start_time} - ${shift.end_time}`} at {shift.location}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Guard: {shift.profiles?.first_name || "N/A"} {shift.profiles?.last_name || ""}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No upcoming shifts scheduled.</p>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>My Recent Incidents</CardTitle>
        </CardHeader>
        <CardContent>
          {loadingClientIncidents ? (
            <p className="text-sm text-muted-foreground">Loading your incidents...</p>
          ) : clientRecentIncidents.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have not reported any incidents recently.</p>
          ) : (
            <div className="space-y-3">
              {clientRecentIncidents.map((incident) => (
                <div key={incident.id} className="border rounded-md p-3">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="font-medium text-sm">{incident.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                      incident.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      incident.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      incident.status === 'Resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {incident.status}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Reported: {format(new Date(incident.created_at), "PPP")} at {incident.location}
                  </p>
                </div>
              ))}
              <Button variant="link" className="p-0 h-auto" asChild>
                <Link to="/client/incidents-comms">View All Incidents</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Separator />

      <Card>
        <CardHeader>
          <CardTitle>Recent Communications</CardTitle>
        </CardHeader>
        <CardContent>
          {loadingCommunications ? (
            <p className="text-sm text-muted-foreground">Loading communications...</p>
          ) : latestCommunication ? (
            <>
              <h3 className="font-medium">{latestCommunication.title}</h3>
              <p className="text-sm text-muted-foreground">
                Received: {format(new Date(latestCommunication.created_at), "PPP, hh:mm a")}
              </p>
              <p className="text-sm mt-2">{latestCommunication.content}</p>
              <Button variant="link" className="p-0 h-auto mt-2" asChild>
                <Link to="/client/incidents-comms">View All Communications</Link>
              </Button>
            </>
          ) : (
            <p className="text-sm text-muted-foreground">No recent communications.</p>
          )}
        </CardContent>
      </Card>

      <Separator />

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-semibold">
              <AlertCircle className="inline-block mr-2 h-5 w-5 text-red-500" />
              Report New Incident
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Quickly report any security incidents you observe.
            </p>
            <Button asChild className="w-full">
              <Link to="/client/report-incident">
                <AlertCircle className="mr-2 h-4 w-4" />
                Submit Incident Report
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xl font-semibold">
              <MessageCircleQuestion className="inline-block mr-2 h-5 w-5 text-purple-500" />
              Submit New Inquiry
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Have a question or need assistance? Submit a new inquiry.
            </p>
            <Button asChild className="w-full" variant="outline">
              <Link to="/client/submit-inquiry">
                <MessageCircleQuestion className="mr-2 h-4 w-4" />
                Submit Inquiry
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <BarChart2 className="inline-block mr-2 h-5 w-5 text-orange-500" />
            View Reports & Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Access detailed reports and analytics related to your incidents and inquiries.
          </p>
          <Button asChild className="w-full" variant="secondary">
            <Link to="/client/reports">
              <BarChart2 className="mr-2 h-4 w-4" />
              View Reports
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientDashboard;