import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { MessageCircle, PlusCircle, Send } from "lucide-react";
import { format } from "date-fns";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

interface SupportChat {
  id: string;
  user_id: string;
  subject: string;
  status: string;
  created_at: string;
  updated_at: string;
  support_messages: SupportMessage[];
}

interface SupportMessage {
  id: string;
  chat_id: string;
  sender_id: string;
  message_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

const GuardSupportChat = () => {
  const { user, loading: authLoading, profile } = useAuth();
  const [chats, setChats] = useState<SupportChat[]>([]);
  const [loadingChats, setLoadingChats] = useState(true);
  const [isNewChatDialogOpen, setIsNewChatDialogOpen] = useState(false);
  const [newChatSubject, setNewChatSubject] = useState("");
  const [newChatMessage, setNewChatMessage] = useState("");
  const [creatingChat, setCreatingChat] = useState(false);
  const [selectedChat, setSelectedChat] = useState<SupportChat | null>(null);
  const [currentMessageText, setCurrentMessageText] = useState("");
  const [sendingMessage, setSendingMessage] = useState(false);

  const fetchChats = async () => {
    if (!user) {
      setLoadingChats(false);
      return;
    }
    setLoadingChats(true);
    const { data, error } = await supabase
      .from("support_chats")
      .select(`
        *,
        support_messages (
          *,
          profiles (
            first_name,
            last_name,
            role
          )
        )
      `)
      .eq("user_id", user.id)
      .order("updated_at", { ascending: false });

    if (error) {
      toast.error("Failed to load support chats: " + error.message);
      console.error("Error fetching support chats:", error);
      setChats([]);
    } else {
      const chatsWithSortedMessages = (data as SupportChat[]).map(chat => ({
        ...chat,
        support_messages: chat.support_messages.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      }));
      setChats(chatsWithSortedMessages);
    }
    setLoadingChats(false);
  };

  useEffect(() => {
    if (!authLoading) {
      fetchChats();
    }

    const chatChannel = supabase
      .channel('guard_support_chat_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'support_chats' },
        (payload) => {
          if (payload.new.user_id === user?.id || payload.old.user_id === user?.id) {
            fetchChats();
          }
        }
      )
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'support_messages' },
        (payload) => {
          if (selectedChat && (payload.new.chat_id === selectedChat.id || payload.old.chat_id === selectedChat.id)) {
            fetchChats(); // Re-fetch to update messages in the currently open chat
          } else if (payload.new.sender_id !== user?.id) { // Notify if a new message comes from someone else
            toast.info(`New message in chat: "${chats.find(c => c.id === payload.new.chat_id)?.subject}"`);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(chatChannel);
    };
  }, [user, authLoading, selectedChat, chats]);

  const handleCreateNewChat = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !newChatSubject.trim() || !newChatMessage.trim()) {
      toast.error("Please provide a subject and an initial message.");
      return;
    }
    setCreatingChat(true);

    const { data: chatData, error: chatError } = await supabase
      .from("support_chats")
      .insert({
        user_id: user.id,
        subject: newChatSubject.trim(),
        status: "Open",
      })
      .select()
      .single();

    if (chatError) {
      toast.error("Failed to create chat: " + chatError.message);
      console.error("Error creating chat:", chatError);
      setCreatingChat(false);
      return;
    }

    const { error: messageError } = await supabase
      .from("support_messages")
      .insert({
        chat_id: chatData.id,
        sender_id: user.id,
        message_text: newChatMessage.trim(),
      });

    if (messageError) {
      toast.error("Failed to send initial message: " + messageError.message);
      console.error("Error sending initial message:", messageError);
      // Consider deleting the chat if initial message fails
      await supabase.from("support_chats").delete().eq("id", chatData.id);
      setCreatingChat(false);
      return;
    }

    toast.success("New support chat created!");
    setNewChatSubject("");
    setNewChatMessage("");
    setIsNewChatDialogOpen(false);
    fetchChats(); // Re-fetch to show the new chat
  };

  const handleSendMessage = async (chatId: string) => {
    if (!user || !currentMessageText.trim()) {
      toast.error("Message cannot be empty.");
      return;
    }
    setSendingMessage(true);

    const { error } = await supabase.from("support_messages").insert({
      chat_id: chatId,
      sender_id: user.id,
      message_text: currentMessageText.trim(),
    });

    if (error) {
      toast.error("Failed to send message: " + error.message);
      console.error("Error sending message:", error);
    } else {
      setCurrentMessageText("");
      // Update chat's updated_at timestamp to bring it to top
      await supabase.from("support_chats").update({ updated_at: new Date().toISOString() }).eq("id", chatId);
      fetchChats(); // Re-fetch to update messages and chat order
    }
    setSendingMessage(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Support Chats</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageCircle className="inline-block mr-2 h-5 w-5 text-blue-500" />
            Your Conversations
          </CardTitle>
          <Button onClick={() => setIsNewChatDialogOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Chat
          </Button>
        </CardHeader>
        <CardContent>
          {loadingChats ? (
            <p className="text-sm text-muted-foreground">Loading chats...</p>
          ) : chats.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have no active support chats. Click "New Chat" to start one.</p>
          ) : (
            <div className="grid gap-4">
              {chats.map((chat) => (
                <Card key={chat.id} className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800" onClick={() => setSelectedChat(chat)}>
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-lg">{chat.subject}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                      chat.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      chat.status === 'Closed' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    }`}>
                      {chat.status}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">Last update: {format(new Date(chat.updated_at), "PPpp")}</p>
                  {chat.support_messages.length > 0 && (
                    <p className="text-sm text-muted-foreground truncate mt-1">
                      Last message: {chat.support_messages[chat.support_messages.length - 1].message_text}
                    </p>
                  )}
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* New Chat Dialog */}
      <Dialog open={isNewChatDialogOpen} onOpenChange={setIsNewChatDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Start New Support Chat</DialogTitle>
            <DialogDescription>
              Describe your issue or question. A field officer will respond shortly.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleCreateNewChat} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="newChatSubject">Subject</Label>
              <Input
                id="newChatSubject"
                placeholder="e.g., Issue with patrol route, Question about policy"
                value={newChatSubject}
                onChange={(e) => setNewChatSubject(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="newChatMessage">Initial Message</Label>
              <Textarea
                id="newChatMessage"
                placeholder="Provide details about your request..."
                value={newChatMessage}
                onChange={(e) => setNewChatMessage(e.target.value)}
                required
                rows={4}
              />
            </div>
            <DialogFooter>
              <Button type="submit" disabled={creatingChat}>
                {creatingChat ? "Creating..." : "Start Chat"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Selected Chat Dialog */}
      {selectedChat && (
        <Dialog open={!!selectedChat} onOpenChange={() => setSelectedChat(null)}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
            <DialogHeader>
              <DialogTitle>{selectedChat.subject}</DialogTitle>
              <DialogDescription>
                Status: <span className="capitalize font-semibold">{selectedChat.status}</span>
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-y-auto p-4 border rounded-md bg-gray-50 dark:bg-gray-800 space-y-3 mb-4">
              {selectedChat.support_messages.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center">No messages yet. Send the first one!</p>
              ) : (
                selectedChat.support_messages.map((message) => (
                  <div key={message.id} className={`flex ${message.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] p-3 rounded-lg ${
                      message.sender_id === user?.id
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-100'
                    }`}>
                      <p className="text-xs font-semibold mb-1">
                        {message.profiles?.first_name || "Unknown"} {message.profiles?.last_name || ""}
                        <span className="text-xs text-muted-foreground ml-1">
                          ({message.profiles?.role || "N/A"})
                        </span>
                      </p>
                      <p className="text-sm">{message.message_text}</p>
                      <p className={`text-xs mt-1 ${message.sender_id === user?.id ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'}`}>
                        {format(new Date(message.created_at), "MMM dd, hh:mm a")}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
            <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(selectedChat.id); }} className="flex gap-2">
              <Textarea
                placeholder="Type your message here..."
                value={currentMessageText}
                onChange={(e) => setCurrentMessageText(e.target.value)}
                rows={1}
                className="flex-1"
                disabled={sendingMessage || selectedChat.status === 'Closed'}
              />
              <Button type="submit" disabled={sendingMessage || selectedChat.status === 'Closed'}>
                <Send className="h-4 w-4" />
              </Button>
            </form>
            {selectedChat.status === 'Closed' && (
              <p className="text-center text-sm text-muted-foreground mt-2">This chat is closed. You cannot send new messages.</p>
            )}
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default GuardSupportChat;