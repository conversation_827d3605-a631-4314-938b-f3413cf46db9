import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { MadeWithDyad } from './made-with-dyad';

describe('MadeWithDyad', () => {
  it('renders the "Made with Dyad" text', () => {
    render(<MadeWithDyad />);
    expect(screen.getByText('Made with Dyad')).toBeInTheDocument();
  });

  it('renders a link to dyad.sh', () => {
    render(<MadeWithDyad />);
    const linkElement = screen.getByRole('link', { name: /Made with Dyad/i });
    expect(linkElement).toBeInTheDocument();
    expect(linkElement).toHaveAttribute('href', 'https://www.dyad.sh/');
    expect(linkElement).toHaveAttribute('target', '_blank');
    expect(linkElement).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('applies correct Tailwind CSS classes', () => {
    render(<MadeWithDyad />);
    const divElement = screen.getByText('Made with Dyad').closest('div');
    expect(divElement).toHaveClass('p-4 text-center');
    const linkElement = screen.getByRole('link', { name: /Made with Dyad/i });
    expect(linkElement).toHaveClass('text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200');
  });
});