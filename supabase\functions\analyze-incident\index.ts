import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { incidentId } = await req.json();
    if (!incidentId) {
      return new Response(JSON.stringify({ error: 'Incident ID is required' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    const { data: incident, error: incidentError } = await supabaseClient
      .from('incidents')
      .select('title, description, location, created_at')
      .eq('id', incidentId)
      .single();

    if (incidentError) {
      console.error('Error fetching incident:', incidentError.message);
      return new Response(JSON.stringify({ error: 'Incident not found or access denied' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    // --- Simulated AI Analysis Logic ---
    const incidentText = `${incident.title} ${incident.description}`.toLowerCase();
    let riskLevel = "Low";
    const keywords: string[] = [];
    const recommendations: string[] = [];

    if (incidentText.includes("weapon") || incidentText.includes("firearm") || incidentText.includes("assault")) {
      riskLevel = "Critical";
      keywords.push("violence", "weapon");
      recommendations.push("Immediately alert emergency services.", "Secure the area and evacuate non-essential personnel.", "Provide first aid if safe to do so.");
    } else if (incidentText.includes("theft") || incidentText.includes("robbery") || incidentText.includes("break-in")) {
      riskLevel = "High";
      keywords.push("theft", "property crime");
      recommendations.push("Review surveillance footage.", "Secure entry points.", "File a police report.");
    } else if (incidentText.includes("suspicious") || incidentText.includes("unauthorized") || incidentText.includes("loitering")) {
      riskLevel = "Medium";
      keywords.push("suspicious activity", "unauthorized access");
      recommendations.push("Increase patrols in the area.", "Verify identity of suspicious individuals.", "Monitor the location closely.");
    } else if (incidentText.includes("damage") || incidentText.includes("vandalism") || incidentText.includes("broken")) {
      riskLevel = "Medium";
      keywords.push("property damage", "vandalism");
      recommendations.push("Assess extent of damage.", "Document with photos.", "Arrange for repairs.");
    } else {
      riskLevel = "Low";
      keywords.push("general observation");
      recommendations.push("Maintain regular patrols.", "Document for future reference.", "No immediate action required.");
    }

    if (incidentText.includes("camera") || incidentText.includes("cctv")) {
      recommendations.push("Review relevant CCTV footage.");
      keywords.push("surveillance");
    }
    if (incidentText.includes("personnel") || incidentText.includes("guard")) {
      recommendations.push("Debrief involved security personnel.");
      keywords.push("personnel involvement");
    }

    const insights = {
      summary: `AI analysis for incident "${incident.title}" reported on ${new Date(incident.created_at).toLocaleDateString()}. The system detected a ${riskLevel} risk level.`,
      keywords: Array.from(new Set(keywords)), // Remove duplicates
      recommendations: Array.from(new Set(recommendations)), // Remove duplicates
      riskLevel: riskLevel,
      trends: "Based on current data, this incident type is " + (riskLevel === "Critical" ? "rare but severe." : riskLevel === "High" ? "concerning and requires attention." : riskLevel === "Medium" ? "common in this area." : "a routine observation."),
    };

    return new Response(JSON.stringify({ incident, insights }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    console.error('Error in analyze-incident function:', error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});