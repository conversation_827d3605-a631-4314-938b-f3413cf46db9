import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { ListTodo, CheckCircle2 } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Task {
  id: string;
  title: string;
  description: string | null;
  user_id: string; // Assigned guard's ID
  status: string;
  priority: string;
  due_date: string | null;
  created_by: string; // Field officer's ID
  created_at: string;
  creator_profile: {
    first_name: string | null;
    last_name: string | null;
  } | null; // Creator's profile
}

const GuardMyTasks = () => {
  const { user, loading: authLoading } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [editStatus, setEditStatus] = useState("");
  const [updateLoading, setUpdateLoading] = useState(false);

  const fetchTasks = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    setLoading(true);
    const { data, error } = await supabase
      .from("tasks")
      .select(`
          *,
          creator_profile:profiles!tasks_created_by_fkey (
            first_name,
            last_name
          )
        `)
      .eq("user_id", user.id)
      .order("due_date", { ascending: true })
      .order("priority", { ascending: false });

    if (error) {
      toast.error("Failed to load your tasks: " + error.message);
      console.error("Error fetching guard tasks:", error);
    } else {
      setTasks(data as Task[]);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!authLoading) {
      fetchTasks();
    }
  }, [user, authLoading]);

  const handleEditClick = (task: Task) => {
    setCurrentTask(task);
    setEditStatus(task.status);
    setIsEditDialogOpen(true);
  };

  const handleUpdateStatus = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentTask) return;

    setUpdateLoading(true);
    const { error } = await supabase
      .from("tasks")
      .update({ status: editStatus })
      .eq("id", currentTask.id);

    if (error) {
      toast.error("Failed to update task status: " + error.message);
      console.error("Task status update error:", error);
    } else {
      toast.success("Task status updated successfully!");
      setIsEditDialogOpen(false);
      fetchTasks(); // Re-fetch tasks to show updated data
    }
    setUpdateLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Tasks</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <ListTodo className="inline-block mr-2 h-5 w-5 text-green-500" />
            My Assigned Tasks
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading tasks...</p>
          ) : tasks.length === 0 ? (
            <p className="text-sm text-muted-foreground">No tasks assigned to you yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Assigned By</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell className="font-medium">{task.title}</TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {task.description || "No description"}
                      </TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          task.status === 'Pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                          task.status === 'In Progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          task.status === 'Completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {task.status}
                        </span>
                      </TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          task.priority === 'Low' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          task.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          task.priority === 'High' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {task.priority}
                        </span>
                      </TableCell>
                      <TableCell>{task.due_date ? format(new Date(task.due_date), "PPP") : "N/A"}</TableCell>
                      <TableCell>
                        {task.creator_profile?.first_name || "N/A"} {task.creator_profile?.last_name || ""}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(task)}>
                          <CheckCircle2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Task Status Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Task Status</DialogTitle>
            <DialogDescription>
              Update the status for the task: "{currentTask?.title}".
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateStatus} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="taskDescription">Description</Label>
              <p id="taskDescription" className="text-sm text-muted-foreground">
                {currentTask?.description || "No description provided."}
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editStatus">Status</Label>
              <Select onValueChange={setEditStatus} value={editStatus}>
                <SelectTrigger id="editStatus">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={updateLoading}>
                {updateLoading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GuardMyTasks;