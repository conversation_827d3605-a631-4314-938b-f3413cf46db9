import React, { useState, useEffect } from 'react';
import { supabase, testSupabaseConnection } from '@/lib/supabaseClient';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const TestConnection: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [envInfo, setEnvInfo] = useState<any>(null);
  const [profileInfo, setProfileInfo] = useState<any>(null);

  useEffect(() => {
    // Check environment variables
    setEnvInfo({
      url: import.meta.env.VITE_SUPABASE_URL,
      hasKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
      keyLength: import.meta.env.VITE_SUPABASE_ANON_KEY?.length || 0
    });
  }, []);

  const testConnection = async () => {
    setConnectionStatus('testing');
    setError(null);
    
    try {
      console.log('Testing Supabase connection...');
      
      // Test basic connection
      const isConnected = await testSupabaseConnection();
      
      if (!isConnected) {
        setConnectionStatus('error');
        setError('Failed to connect to Supabase');
        return;
      }
      
      // Get session info
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Session error:', sessionError);
        setError(`Session error: ${sessionError.message}`);
      }
      
      setSessionInfo({
        hasSession: !!session,
        userId: session?.user?.id || null,
        userEmail: session?.user?.email || null,
        sessionError: sessionError?.message || null
      });

      // If we have a session, try to fetch profile info
      if (session?.user?.id) {
        try {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          setProfileInfo({
            hasProfile: !!profileData,
            role: profileData?.role || null,
            firstName: profileData?.first_name || null,
            lastName: profileData?.last_name || null,
            organizationId: profileData?.organization_id || null,
            profileError: profileError?.message || null
          });
        } catch (profileErr) {
          setProfileInfo({
            hasProfile: false,
            profileError: profileErr instanceof Error ? profileErr.message : 'Unknown profile error'
          });
        }
      }

      setConnectionStatus('success');
      
    } catch (err) {
      console.error('Connection test error:', err);
      setConnectionStatus('error');
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-4">
      <h1 className="text-2xl font-bold">Supabase Connection Test</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Environment Variables</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div>
              <strong>VITE_SUPABASE_URL:</strong> {envInfo?.url || 'Not set'}
            </div>
            <div>
              <strong>VITE_SUPABASE_ANON_KEY:</strong> {envInfo?.hasKey ? `Set (${envInfo.keyLength} chars)` : 'Not set'}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Connection Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testConnection} 
            disabled={connectionStatus === 'testing'}
            className="w-full"
          >
            {connectionStatus === 'testing' ? 'Testing...' : 'Test Connection'}
          </Button>
          
          <div className="space-y-2">
            <div>
              <strong>Status:</strong> 
              <span className={`ml-2 px-2 py-1 rounded text-white ${
                connectionStatus === 'success' ? 'bg-green-500' : 
                connectionStatus === 'error' ? 'bg-red-500' : 
                connectionStatus === 'testing' ? 'bg-yellow-500' : 'bg-gray-500'
              }`}>
                {connectionStatus}
              </span>
            </div>
            
            {error && (
              <div>
                <strong>Error:</strong>
                <div className="text-red-600 bg-red-50 p-2 rounded mt-1">{error}</div>
              </div>
            )}
            
            {sessionInfo && (
              <div>
                <strong>Session Info:</strong>
                <div className="bg-gray-50 p-2 rounded mt-1">
                  <div>Has Session: {sessionInfo.hasSession ? 'Yes' : 'No'}</div>
                  <div>User ID: {sessionInfo.userId || 'None'}</div>
                  <div>User Email: {sessionInfo.userEmail || 'None'}</div>
                  {sessionInfo.sessionError && (
                    <div className="text-red-600">Session Error: {sessionInfo.sessionError}</div>
                  )}
                </div>
              </div>
            )}

            {profileInfo && (
              <div>
                <strong>Profile Info:</strong>
                <div className="bg-blue-50 p-2 rounded mt-1">
                  <div>Has Profile: {profileInfo.hasProfile ? 'Yes' : 'No'}</div>
                  <div>Role: {profileInfo.role || 'None'}</div>
                  <div>Name: {profileInfo.firstName} {profileInfo.lastName}</div>
                  <div>Organization ID: {profileInfo.organizationId || 'None'}</div>
                  {profileInfo.profileError && (
                    <div className="text-red-600">Profile Error: {profileInfo.profileError}</div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestConnection;
