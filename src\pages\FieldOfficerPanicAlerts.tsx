import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { AlertTriangle, CheckCircle2, MapPin } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

interface PanicAlert {
  id: string;
  user_id: string;
  status: string;
  created_at: string;
  latitude: number | null;
  longitude: number | null;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

interface LiveGuardLocation {
  latitude: number;
  longitude: number;
  timestamp: string;
}

const FieldOfficerPanicAlerts = () => {
  const [alerts, setAlerts] = useState<PanicAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentAlert, setCurrentAlert] = useState<PanicAlert | null>(null);
  const [editStatus, setEditStatus] = useState("");
  const [updateLoading, setUpdateLoading] = useState(false);
  const [liveGuardLocation, setLiveGuardLocation] = useState<LiveGuardLocation | null>(null);
  const [loadingLiveLocation, setLoadingLiveLocation] = useState(false);

  const fetchPanicAlerts = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from("panic_alerts")
      .select(`
          *,
          profiles (
            first_name,
            last_name
          )
        `)
      .order("created_at", { ascending: false });

    if (error) {
      toast.error("Failed to load panic alerts: " + error.message);
      console.error("Error fetching panic alerts:", error);
    } else {
      setAlerts(data as PanicAlert[]);
    }
    setLoading(false);
  };

  const fetchLiveGuardLocation = async (userId: string) => {
    setLoadingLiveLocation(true);
    const { data, error } = await supabase
      .from("guard_locations")
      .select("latitude, longitude, timestamp")
      .eq("user_id", userId)
      .order("timestamp", { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
      console.error("Error fetching live guard location:", error.message);
      setLiveGuardLocation(null);
    } else if (data) {
      setLiveGuardLocation(data);
    } else {
      setLiveGuardLocation(null);
    }
    setLoadingLiveLocation(false);
  };

  useEffect(() => {
    fetchPanicAlerts();

    // Set up real-time subscription for panic alerts
    const channel = supabase
      .channel('panic_alerts_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'panic_alerts' },
        (payload) => {
          console.log('Panic alert change received!', payload);
          fetchPanicAlerts(); // Re-fetch data on any change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  const handleEditClick = (alert: PanicAlert) => {
    setCurrentAlert(alert);
    setEditStatus(alert.status);
    setIsEditDialogOpen(true);
    fetchLiveGuardLocation(alert.user_id); // Fetch live location when dialog opens
  };

  const handleUpdateStatus = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentAlert) return;

    setUpdateLoading(true);
    const { error } = await supabase
      .from("panic_alerts")
      .update({ status: editStatus })
      .eq("id", currentAlert.id);

    if (error) {
      toast.error("Failed to update alert status: " + error.message);
      console.error("Panic alert status update error:", error);
    } else {
      toast.success("Alert status updated successfully!");
      setIsEditDialogOpen(false);
      fetchPanicAlerts(); // Re-fetch alerts to show updated data
    }
    setUpdateLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Panic Alerts Overview</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <AlertTriangle className="inline-block mr-2 h-5 w-5 text-red-500" />
            All Panic Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading alerts...</p>
          ) : alerts.length === 0 ? (
            <p className="text-sm text-muted-foreground">No panic alerts reported yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Time</TableHead>
                    <TableHead>Guard</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {alerts.map((alert) => (
                    <TableRow key={alert.id}>
                      <TableCell className="font-medium">
                        {format(new Date(alert.created_at), "PPpp")}
                      </TableCell>
                      <TableCell>
                        {alert.profiles?.first_name || "N/A"} {alert.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          alert.status === 'Active' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        }`}>
                          {alert.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        {alert.latitude && alert.longitude ? (
                          <a
                            href={`https://www.google.com/maps/search/?api=1&query=${alert.latitude},${alert.longitude}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline flex items-center"
                          >
                            <MapPin className="h-4 w-4 mr-1" /> View Map
                          </a>
                        ) : (
                          "N/A"
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(alert)}>
                          <CheckCircle2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Alert Status Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Panic Alert Status</DialogTitle>
            <DialogDescription>
              Update the status for the alert from {currentAlert?.profiles?.first_name} {currentAlert?.profiles?.last_name}.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateStatus} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="alertTime">Alert Time (Recorded)</Label>
              <p id="alertTime" className="text-sm text-muted-foreground">
                {currentAlert ? format(new Date(currentAlert.created_at), "PPpp") : "N/A"}
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="alertLocation">Alert Location (Recorded)</Label>
              <p id="alertLocation" className="text-sm text-muted-foreground">
                {currentAlert?.latitude && currentAlert?.longitude ? (
                  <a
                    href={`https://www.google.com/maps/search/?api=1&query=${currentAlert.latitude},${currentAlert.longitude}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline flex items-center"
                  >
                    <MapPin className="h-4 w-4 mr-1" /> {currentAlert.latitude.toFixed(6)}, {currentAlert.longitude.toFixed(6)}
                  </a>
                ) : (
                  "Location not available"
                )}
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="liveLocation">Current Guard Location</Label>
              {loadingLiveLocation ? (
                <p className="text-sm text-muted-foreground">Loading live location...</p>
              ) : liveGuardLocation ? (
                <p id="liveLocation" className="text-sm text-muted-foreground">
                  <a
                    href={`https://www.google.com/maps/search/?api=1&query=${liveGuardLocation.latitude},${liveGuardLocation.longitude}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline flex items-center"
                  >
                    <MapPin className="h-4 w-4 mr-1" /> {liveGuardLocation.latitude.toFixed(6)}, {liveGuardLocation.longitude.toFixed(6)}
                  </a>
                  <span className="block text-xs mt-1">Last updated: {format(new Date(liveGuardLocation.timestamp), "PPpp")}</span>
                </p>
              ) : (
                <p className="text-sm text-muted-foreground">Live location not available or guard offline.</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editStatus">Status</Label>
              <Select onValueChange={setEditStatus} value={editStatus}>
                <SelectTrigger id="editStatus">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Resolved">Resolved</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={updateLoading}>
                {updateLoading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FieldOfficerPanicAlerts;