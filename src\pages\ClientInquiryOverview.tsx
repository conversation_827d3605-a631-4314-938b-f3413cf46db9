import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { MessageCircleQuestion, MessageSquare } from "lucide-react"; // Added MessageSquare icon
import { format } from "date-fns";
import { Textarea } from "@/components/ui/textarea"; // Import Textarea
import { Button } from "@/components/ui/button"; // Import Button

interface ClientInquiry {
  id: string;
  client_id: string;
  subject: string;
  message: string;
  status: string;
  created_at: string;
  client_inquiry_comments: InquiryComment[]; // Added comments to the interface
}

interface InquiryComment {
  id: string;
  inquiry_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

const ClientInquiryOverview = () => {
  const { user, loading: authLoading } = useAuth();
  const [inquiries, setInquiries] = useState<ClientInquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [newCommentText, setNewCommentText] = useState<{ [key: string]: string }>({});
  const [commentLoading, setCommentLoading] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const fetchInquiries = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      setLoading(true);
      const { data, error } = await supabase
        .from("client_inquiries")
        .select(`
          *,
          client_inquiry_comments (
            id,
            user_id,
            comment_text,
            created_at,
            profiles (
              first_name,
              last_name,
              role
            )
          )
        `)
        .eq("client_id", user.id)
        .order("created_at", { ascending: false });

      if (error) {
        toast.error("Failed to load your inquiries: " + error.message);
        console.error("Error fetching client inquiries:", error);
        setInquiries([]);
      } else {
        // Sort comments within each inquiry by created_at
        const inquiriesWithSortedComments = (data as ClientInquiry[]).map(inquiry => ({
          ...inquiry,
          client_inquiry_comments: inquiry.client_inquiry_comments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        }));
        setInquiries(inquiriesWithSortedComments);
      }
      setLoading(false);
    };

    if (!authLoading) {
      fetchInquiries();
    }

    // Set up real-time subscription for client inquiries and comments
    const inquiriesChannel = supabase
      .channel('client_inquiry_overview_inquiries_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'client_inquiries' },
        () => {
          fetchInquiries();
        }
      )
      .subscribe();

    const commentsChannel = supabase
      .channel('client_inquiry_overview_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'client_inquiry_comments' },
        () => {
          fetchInquiries();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(inquiriesChannel);
      supabase.removeChannel(commentsChannel);
    };
  }, [user, authLoading]);

  const handleAddComment = async (inquiryId: string) => {
    const commentText = newCommentText[inquiryId]?.trim();
    if (!user) {
      toast.error("You must be logged in to add a comment.");
      return;
    }
    if (!commentText) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setCommentLoading(prev => ({ ...prev, [inquiryId]: true }));
    const { error } = await supabase.from("client_inquiry_comments").insert({
      inquiry_id: inquiryId,
      user_id: user.id,
      comment_text: commentText,
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewCommentText(prev => ({ ...prev, [inquiryId]: "" }));
    }
    setCommentLoading(prev => ({ ...prev, [inquiryId]: false }));
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Inquiries</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MessageCircleQuestion className="inline-block mr-2 h-5 w-5 text-purple-500" />
            My Submitted Inquiries
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading inquiries...</p>
          ) : inquiries.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have not submitted any inquiries yet.</p>
          ) : (
            <div className="grid gap-4"> {/* Changed to grid for better spacing of cards */}
              {inquiries.map((inquiry) => (
                <Card key={inquiry.id} className="p-4"> {/* Each inquiry as a Card */}
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-lg">{inquiry.subject}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                      inquiry.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      inquiry.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      inquiry.status === 'Resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {inquiry.status}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Submitted: {format(new Date(inquiry.created_at), "PPP, hh:mm a")}
                  </p>
                  <p className="text-sm mt-2">{inquiry.message}</p>

                  {/* Inquiry Comments Section */}
                  {inquiry.client_inquiry_comments && inquiry.client_inquiry_comments.length > 0 && (
                    <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <h4 className="text-md font-semibold mb-2 flex items-center">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Comments
                      </h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
                        {inquiry.client_inquiry_comments.map((comment) => (
                          <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                            <p className="text-xs font-medium">
                              {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({comment.profiles?.role || "N/A"})
                              </span>
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {format(new Date(comment.created_at), "PPpp")}
                            </p>
                            <p className="text-sm mt-1">{comment.comment_text}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  <form onSubmit={(e) => { e.preventDefault(); handleAddComment(inquiry.id); }} className="mt-4 flex gap-2">
                    <Textarea
                      placeholder="Add a new comment..."
                      value={newCommentText[inquiry.id] || ""}
                      onChange={(e) => setNewCommentText(prev => ({ ...prev, [inquiry.id]: e.target.value }))}
                      rows={2}
                      className="flex-1"
                      disabled={commentLoading[inquiry.id]}
                    />
                    <Button type="submit" disabled={commentLoading[inquiry.id]}>
                      {commentLoading[inquiry.id] ? "Adding..." : "Add Comment"}
                    </Button>
                  </form>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientInquiryOverview;