import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { supabase, testSupabaseConnection } from "@/lib/supabaseClient";
import { Session, User } from "@supabase/supabase-js";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { PricingPlan, getPlanDetails } from "@/lib/pricingPlans"; // Import pricing plans

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
  avatar_url: string | null;
  organization_id: string | null;
}

interface Organization {
  id: string;
  name: string;
  plan_type: string;
  current_guards_count: number;
  current_clients_count: number;
  current_shifts_this_month: number;
  current_timesheets_this_month: number;
  last_monthly_reset_date: string;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  organization: Organization | null;
  plan: PricingPlan | undefined;
  loading: boolean;
  error: string | null;
  connectionStatus: 'connecting' | 'connected' | 'error';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [plan, setPlan] = useState<PricingPlan | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error'>('connecting');
  const navigate = useNavigate();

  // Timeout for authentication loading
  const AUTH_TIMEOUT = 30000; // 30 seconds

  // Function to fetch profile and organization data
  const fetchProfileAndOrganization = async (userId: string) => {
    try {
      console.log("AuthContext: fetchProfileAndOrganization started for user:", userId);
      setError(null);

      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*, organization_id")
        .eq("id", userId)
        .single();

      if (profileError) {
        console.error("AuthContext: Error fetching profile:", profileError);
        setError(`Failed to fetch profile: ${profileError.message}`);
        setProfile(null);
        setOrganization(null);
        setPlan(undefined);
        setLoading(false);
        return;
      }

      console.log("AuthContext: Profile data fetched:", profileData);
      setProfile(profileData);
      let currentOrg: Organization | null = null;
      let currentPlan: PricingPlan | undefined = undefined;

      if (profileData?.organization_id) {
        const { data: orgData, error: orgError } = await supabase
          .from("organizations")
          .select("*")
          .eq("id", profileData.organization_id)
          .single();

        if (orgError) {
          console.error("AuthContext: Error fetching organization:", orgError);
          setError(`Failed to fetch organization: ${orgError.message}`);
          currentOrg = null;
          currentPlan = undefined;
        } else {
          currentOrg = orgData;
          currentPlan = getPlanDetails(orgData.plan_type);
        }
      }

      console.log("AuthContext: Organization data fetched:", currentOrg);
      console.log("AuthContext: Plan details:", currentPlan);
      setOrganization(currentOrg);
      setPlan(currentPlan);
      setLoading(false);

      // Redirect based on role after all data is set
      console.log(`AuthContext: User role: ${profileData?.role}, Attempting to redirect...`);
      if (profileData?.role === "guard") {
        navigate("/guard");
      } else if (profileData?.role === "client") {
        navigate("/client");
      } else if (profileData?.role === "field_officer") {
        navigate("/field-officer");
      } else if (profileData?.role === "company_admin") {
        navigate("/admin");
      } else {
        navigate("/");
      }
    } catch (error) {
      console.error("AuthContext: Unexpected error in fetchProfileAndOrganization:", error);
      setError(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setLoading(false);
    }
  };

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    // Test Supabase connection first
    const initializeAuth = async () => {
      try {
        console.log("AuthContext: Initializing authentication...");
        setConnectionStatus('connecting');

        // Test connection
        const isConnected = await testSupabaseConnection();
        if (!isConnected) {
          setConnectionStatus('error');
          setError('Failed to connect to authentication service');
          setLoading(false);
          return;
        }

        setConnectionStatus('connected');
        console.log("AuthContext: Supabase connection successful");

        // Set up auth state listener
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log("AuthContext: Auth state changed:", event, "User ID:", session?.user?.id);
            setSession(session);
            setUser(session?.user || null);
            setError(null);

            if (session?.user) {
              await fetchProfileAndOrganization(session.user.id);
            } else {
              setProfile(null);
              setOrganization(null);
              setPlan(undefined);
              setLoading(false);
              if (event === 'SIGNED_OUT') {
                navigate("/login");
              }
            }
          }
        );

        // Initial check for session on component mount
        const getInitialSession = async () => {
          try {
            const { data: { session }, error } = await supabase.auth.getSession();
            if (error) {
              console.error("AuthContext: Error getting initial session:", error);
              setError(`Failed to get session: ${error.message}`);
              setLoading(false);
              return;
            }
            setSession(session);
            setUser(session?.user || null);
            if (session?.user) {
              await fetchProfileAndOrganization(session.user.id);
            } else {
              setLoading(false);
            }
          } catch (error) {
            console.error("AuthContext: Unexpected error getting initial session:", error);
            setError(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`);
            setLoading(false);
          }
        };

        await getInitialSession();

        // Cleanup function
        return () => {
          subscription.unsubscribe();
        };

      } catch (error) {
        console.error("AuthContext: Error initializing auth:", error);
        setConnectionStatus('error');
        setError(`Authentication initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setLoading(false);
      }
    };

    // Set timeout for authentication initialization
    timeoutId = setTimeout(() => {
      if (loading) {
        console.error("AuthContext: Authentication initialization timed out");
        setError('Authentication initialization timed out. Please refresh the page.');
        setLoading(false);
        setConnectionStatus('error');
      }
    }, AUTH_TIMEOUT);

    initializeAuth();

    return () => {
      clearTimeout(timeoutId);
    };
  }, [navigate]);

  return (
    <AuthContext.Provider value={{ session, user, profile, organization, plan, loading, error, connectionStatus }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};