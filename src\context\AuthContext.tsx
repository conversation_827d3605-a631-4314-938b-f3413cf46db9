import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { supabase } from "@/lib/supabaseClient";
import { Session, User } from "@supabase/supabase-js";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
  avatar_url: string | null; // Added avatar_url
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange( // Corrected destructuring
      async (event, session) => {
        console.log("AuthContext: Auth state changed:", event, "User ID:", session?.user?.id);
        setSession(session);
        setUser(session?.user || null);

        if (session?.user) {
          console.log("AuthContext: User found, fetching profile...");
          await fetchProfile(session.user.id);
        } else {
          console.log("AuthContext: No user session, clearing data...");
          setProfile(null);
          setLoading(false); // Set loading to false when no user
          if (event === 'SIGNED_OUT') {
            console.log("AuthContext: User signed out, redirecting to login");
            navigate("/login"); // Redirect to login on sign out
          }
        }
      }
    );

    const fetchProfile = async (userId: string) => {
      try {
        console.log("AuthContext: Fetching profile for user:", userId);
        const { data, error } = await supabase
          .from("profiles")
          .select("*, avatar_url") // Select avatar_url
          .eq("id", userId)
          .single();

        if (error) {
          console.error("AuthContext: Error fetching profile:", error.message);
          setProfile(null);
        } else {
          console.log("AuthContext: Profile data fetched:", data);
          setProfile(data);

          // Redirect based on role after fetching profile
          console.log("AuthContext: User role:", data?.role, "Attempting to redirect...");
          if (data?.role === "guard") {
            console.log("AuthContext: Redirecting to guard dashboard");
            navigate("/guard");
          } else if (data?.role === "client") {
            console.log("AuthContext: Redirecting to client dashboard");
            navigate("/client");
          } else if (data?.role === "field_officer") {
            console.log("AuthContext: Redirecting to field officer dashboard");
            navigate("/field-officer");
          } else if (data?.role === "company_admin") {
            console.log("AuthContext: Redirecting to admin dashboard");
            navigate("/admin");
          } else {
            console.log("AuthContext: No specific role found, redirecting to home");
            navigate("/"); // Default redirect if role is not recognized or null
          }
        }
      } catch (err) {
        console.error("AuthContext: Exception while fetching profile:", err);
        setProfile(null);
      } finally {
        // Always set loading to false after profile fetch attempt
        console.log("AuthContext: Setting loading to false");
        setLoading(false);
      }
    };

    // Initial session check
    const getInitialSession = async () => {
      try {
        console.log("AuthContext: Getting initial session...");
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error("AuthContext: Error getting initial session:", error);
          setLoading(false);
          return;
        }

        console.log("AuthContext: Initial session:", session?.user?.id ? "Found" : "None");
        setSession(session);
        setUser(session?.user || null);

        if (session?.user) {
          await fetchProfile(session.user.id);
        } else {
          setLoading(false); // Set loading to false even if no session
        }
      } catch (err) {
        console.error("AuthContext: Exception during initial session check:", err);
        setLoading(false);
      }
    };

    getInitialSession();

    return () => {
      subscription.unsubscribe(); // Corrected call
    };
  }, [navigate]);

  return (
    <AuthContext.Provider value={{ session, user, profile, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};