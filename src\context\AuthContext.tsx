import React, { createContext, useState, useEffect, ReactNode, useCallback } from "react";
import { supabase } from "@/lib/supabaseClient";
import { Session, User } from "@supabase/supabase-js";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { PricingPlan, getPlanDetails } from "@/lib/pricingPlans";

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
  avatar_url: string | null;
  organization_id: string | null;
}

interface Organization {
  id: string;
  name: string;
  plan_type: string;
  current_guards_count: number;
  current_clients_count: number;
  current_shifts_this_month: number;
  current_timesheets_this_month: number;
  last_monthly_reset_date: string;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  organization: Organization | null;
  plan: PricingPlan | undefined;
  loading: boolean;
  error: string | null;
  connectionStatus: 'connected' | 'connecting' | 'error';
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [plan, setPlan] = useState<PricingPlan | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'error'>('connecting');
  const navigate = useNavigate();

  // Function to fetch profile and organization data
  const fetchProfileAndOrganization = useCallback(async (userId: string) => {
    setLoading(true);
    setError(null);
    setConnectionStatus('connecting');

    try {
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("*, organization_id")
        .eq("id", userId)
        .single();

      if (profileError) {
        console.error("Error fetching profile:", profileError.message);
        setProfile(null);
        setOrganization(null);
        setPlan(undefined);
        setError("Failed to load user profile.");
        setConnectionStatus('error');
        return;
      }

      setProfile(profileData);
      let currentOrg: Organization | null = null;
      let currentPlan: PricingPlan | undefined = undefined;

      if (profileData?.organization_id) {
        const { data: orgData, error: orgError } = await supabase
          .from("organizations")
          .select("*")
          .eq("id", profileData.organization_id)
          .single();

        if (orgError) {
          console.error("Error fetching organization:", orgError.message);
          currentOrg = null;
          currentPlan = undefined;
          setError("Failed to load organization data.");
          setConnectionStatus('error');
        } else {
          currentOrg = orgData;
          currentPlan = getPlanDetails(orgData.plan_type);
        }
      }
      setOrganization(currentOrg);
      setPlan(currentPlan);
      setConnectionStatus('connected');

      // Redirect based on role after all data is set
      console.log(`User role: ${profileData?.role}, Attempting to redirect...`);
      if (profileData?.role === "guard") {
        navigate("/guard");
      } else if (profileData?.role === "client") {
        navigate("/client");
      } else if (profileData?.role === "field_officer") {
        navigate("/field-officer");
      } else if (profileData?.role === "company_admin") {
        navigate("/admin");
      } else {
        navigate("/");
      }
    } catch (err: unknown) { // Changed from any to unknown
      console.error("Unexpected error in fetchProfileAndOrganization:", (err as Error).message);
      setError("An unexpected error occurred during profile loading.");
      setConnectionStatus('error');
    } finally {
      setLoading(false);
    }
  }, [navigate]); // Added navigate to useCallback dependencies

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log("Auth state changed:", event, "User ID:", session?.user?.id);
        setSession(session);
        setUser(session?.user || null);
        setError(null);

        if (session?.user) {
          await fetchProfileAndOrganization(session.user.id);
        } else {
          setProfile(null);
          setOrganization(null);
          setPlan(undefined);
          setLoading(false);
          setConnectionStatus('connected');
          if (event === 'SIGNED_OUT') {
            navigate("/login");
          }
        }
      }
    );

    // Initial check for session and connection status
    supabase.auth.getSession().then(({ data: { session }, error: getSessionError }) => {
      if (getSessionError) {
        console.error("Error getting initial session:", getSessionError.message);
        setError(`Initial session check failed: ${getSessionError.message}`);
        setConnectionStatus('error');
        setLoading(false);
        return;
      }
      if (!session) {
        setLoading(false);
        setConnectionStatus('connected');
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [navigate, fetchProfileAndOrganization]); // Added fetchProfileAndOrganization to useEffect dependencies

  return (
    <AuthContext.Provider value={{ session, user, profile, organization, plan, loading, error, connectionStatus }}>
      {children}
    </AuthContext.Provider>
  );
};

// Moved useAuth to its own file: src/hooks/useAuth.ts