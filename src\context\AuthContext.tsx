import { createContext, useContext, useState, useEffect, type ReactNode } from "react";
import { supabase } from "@/lib/supabaseClient";
import { Session, User } from "@supabase/supabase-js";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { PricingPlan, getPlanDetails } from "@/lib/pricingPlans";

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
  avatar_url: string | null;
  organization_id: string | null;
}

interface Organization {
  id: string;
  name: string;
  plan_type: string;
  current_guards_count: number;
  current_clients_count: number;
  current_shifts_this_month: number;
  current_timesheets_this_month: number;
  last_monthly_reset_date: string;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  organization: Organization | null;
  plan: PricingPlan | undefined;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type UserRole = 'guard' | 'client' | 'field_officer' | 'company_admin';

const routes: Record<UserRole, string> = {
  guard: "/guard/dashboard",
  client: "/client/dashboard",
  field_officer: "/field-officer/dashboard",
  company_admin: "/admin/dashboard"
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [plan, setPlan] = useState<PricingPlan | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

 HEAD
  const handleAuthStateChange = async (session: Session | null) => {
    try {
      if (!session?.user) {
        setProfile(null);
        setOrganization(null);
        setPlan(undefined);
        return;
      }

      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select(`*, organizations!inner (*)`)
        .eq("id", session.user.id)
        .single();

      if (profileError) throw profileError;

      const newProfile = {
        id: profileData.id,
        first_name: profileData.first_name,
        last_name: profileData.last_name,
        role: profileData.role,
        avatar_url: profileData.avatar_url,
        organization_id: profileData.organization_id
      };

      setProfile(newProfile);

      if (profileData.organizations) {
        setOrganization(profileData.organizations);
        setPlan(getPlanDetails(profileData.organizations.plan_type));
      }

      if (newProfile.role) {
        type UserRole = 'guard' | 'client' | 'field_officer' | 'company_admin';

        const routes: Record<UserRole, string> = {
          guard: "/guard/dashboard",
          client: "/client/dashboard",
          field_officer: "/field-officer/dashboard",
          company_admin: "/admin/dashboard"
        };

        if (newProfile.role && isValidRole(newProfile.role)) {
          navigate(routes[newProfile.role]);
        }

        function isValidRole(role: string | null): role is UserRole {
          return role !== null && role in routes;
        }
      }
    } catch (error) {
      console.error("Error in auth state change:", error);
      toast.error("Failed to load profile data");

  // Function to fetch profile and organization data
  const fetchProfileAndOrganization = async (userId: string) => {
    setLoading(true); // Set loading true at the start of fetching
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("*, organization_id")
      .eq("id", userId)
      .single();

    if (profileError) {
      console.error("Error fetching profile:", profileError.message);
      setProfile(null);
      setOrganization(null);
      setPlan(undefined);
      setLoading(false); // Set loading false on error
      return;
    }

    setProfile(profileData);
    let currentOrg: Organization | null = null;
    let currentPlan: PricingPlan | undefined = undefined;

    if (profileData?.organization_id) {
      const { data: orgData, error: orgError } = await supabase
        .from("organizations")
        .select("*")
        .eq("id", profileData.organization_id)
        .single();

      if (orgError) {
        console.error("Error fetching organization:", orgError.message);
        currentOrg = null;
        currentPlan = undefined;
      } else {
        currentOrg = orgData;
        currentPlan = getPlanDetails(orgData.plan_type);
      }
    }
    setOrganization(currentOrg);
    setPlan(currentPlan);
    setLoading(false); // Set loading false after all data is fetched

    // Redirect based on role after all data is set
    console.log(`User role: ${profileData?.role}, Attempting to redirect...`);
    if (profileData?.role === "guard") {
      navigate("/guard");
    } else if (profileData?.role === "client") {
      navigate("/client");
    } else if (profileData?.role === "field_officer") {
      navigate("/field-officer");
    } else if (profileData?.role === "company_admin") {
      navigate("/admin");
    } else {
      navigate("/");
 45d08f7 ([dyad] Updated AuthContext to fix client login redirection - wrote 1 file(s))
    }
  };

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
 HEAD
      async (_event, session) => {
        setSession(session);
        setUser(session?.user || null);
        await handleAuthStateChange(session);
        setLoading(false);
      }
    );

    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user || null);
      if (session) {
        handleAuthStateChange(session);
      }
      setLoading(false);
    });

      async (event, session) => {
        console.log("Auth state changed:", event, "User ID:", session?.user?.id);
        setSession(session);
        setUser(session?.user || null);

        if (session?.user) {
          await fetchProfileAndOrganization(session.user.id);
        } else {
          setProfile(null);
          setOrganization(null);
          setPlan(undefined);
          setLoading(false); // Ensure loading is false when no user
          if (event === 'SIGNED_OUT') {
            navigate("/login");
          }
        }
      }
    );

    // The initial session is handled by 'onAuthStateChange' with 'INITIAL_SESSION' event.
    // No need for a separate getSession() call here, as it can cause race conditions.
    // The first event from onAuthStateChange will be 'INITIAL_SESSION' or 'SIGNED_OUT'.
 45d08f7 ([dyad] Updated AuthContext to fix client login redirection - wrote 1 file(s))

    return () => {
      subscription.unsubscribe();
    };
<<<<<<< HEAD
  }, [navigate, handleAuthStateChange]);
=======
  }, [navigate]); // navigate is a stable reference from react-router-dom v6
>>>>>>> 45d08f7 ([dyad] Updated AuthContext to fix client login redirection - wrote 1 file(s))

  return (
    <AuthContext.Provider value={{ session, user, profile, organization, plan, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};