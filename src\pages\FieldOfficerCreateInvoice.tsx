import React, { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useNavigate } from "react-router-dom";
import { ReceiptText, DollarSign } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";

interface ClientProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
}

interface Service {
  id: string;
  name: string;
  price: number;
}

const FieldOfficerCreateInvoice = () => {
  const [clients, setClients] = useState<ClientProfile[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string>("");
  const [selectedServiceId, setSelectedServiceId] = useState<string>("");
  const [amount, setAmount] = useState<string>(""); // Can be pre-filled by service price
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchClientsAndServices = async () => {
      // Fetch Clients
      const { data: clientsData, error: clientsError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .eq("role", "client");

      if (clientsError) {
        toast.error("Failed to load clients: " + clientsError.message);
        console.error("Error fetching clients:", clientsError);
      } else {
        setClients(clientsData || []);
      }

      // Fetch Services
      const { data: servicesData, error: servicesError } = await supabase
        .from("services")
        .select("id, name, price");

      if (servicesError) {
        toast.error("Failed to load services: " + servicesError.message);
        console.error("Error fetching services:", servicesError);
      } else {
        setServices(servicesData || []);
      }
    };
    fetchClientsAndServices();
  }, []);

  // Effect to update amount when a service is selected
  useEffect(() => {
    if (selectedServiceId) {
      const service = services.find(s => s.id === selectedServiceId);
      if (service) {
        setAmount(service.price.toFixed(2));
      }
    } else {
      setAmount(""); // Clear amount if no service is selected
    }
  }, [selectedServiceId, services]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!selectedClientId || !selectedServiceId || !amount || parseFloat(amount) <= 0) {
      toast.error("Please select a client, a service, and ensure the amount is valid.");
      setLoading(false);
      return;
    }

    const { error } = await supabase.from("invoices").insert({
      client_id: selectedClientId,
      service_id: selectedServiceId,
      amount: parseFloat(amount),
      status: "pending", // Default status
      due_date: dueDate ? format(dueDate, "yyyy-MM-dd") : null,
    });

    if (error) {
      toast.error("Failed to create invoice: " + error.message);
      console.error("Invoice creation error:", error);
    } else {
      toast.success("Invoice created successfully!");
      setSelectedClientId("");
      setSelectedServiceId("");
      setAmount("");
      setDueDate(undefined);
      // Optionally navigate to an invoice overview page
      // navigate("/field-officer/invoices-overview");
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Create New Invoice</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="mr-2 h-5 w-5 text-indigo-500" />
            Generate Invoice
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="client">Select Client</Label>
              <Select onValueChange={setSelectedClientId} value={selectedClientId}>
                <SelectTrigger id="client">
                  <SelectValue placeholder="Select a client" />
                </SelectTrigger>
                <SelectContent>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.first_name} {client.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="service">Select Service</Label>
              <Select onValueChange={setSelectedServiceId} value={selectedServiceId}>
                <SelectTrigger id="service">
                  <SelectValue placeholder="Select a service" />
                </SelectTrigger>
                <SelectContent>
                  {services.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {service.name} (₹{service.price.toFixed(2)})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="amount">Amount (INR)</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="e.g., 10000.00"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                required
                disabled={!!selectedServiceId} // Disable if service is selected
              />
              {selectedServiceId && (
                <p className="text-xs text-muted-foreground">Amount is pre-filled based on selected service.</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="dueDate">Due Date (Optional)</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dueDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={setDueDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Creating Invoice..." : "Create Invoice"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerCreateInvoice;