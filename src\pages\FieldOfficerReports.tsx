import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { BarChart2, ListChecks } from "lucide-react"; // Added ListChecks icon
import React, { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";
import { toast } from "sonner";
import IncidentStatusChart from "@/components/IncidentStatusChart";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface IncidentStatusData {
  name: string;
  value: number;
}

interface HoursWorkedData {
  name: string; // Guard's full name
  hours: number;
}

interface TaskCompletionData {
  guardName: string;
  totalTasks: number;
  completedTasks: number;
  completionRate: string; // Percentage string
}

const FieldOfficerReports = () => {
  const [incidentCount, setIncidentCount] = useState<number | null>(null);
  const [timesheetCount, setTimesheetCount] = useState<number | null>(null);
  const [shiftCount, setShiftCount] = useState<number | null>(null);
  const [incidentStatusData, setIncidentStatusData] = useState<IncidentStatusData[]>([]);
  const [hoursWorkedData, setHoursWorkedData] = useState<HoursWorkedData[]>([]);
  const [taskCompletionData, setTaskCompletionData] = useState<TaskCompletionData[]>([]); // New state for task completion data
  const [loading, setLoading] = useState(true);
  const [loadingHoursWorked, setLoadingHoursWorked] = useState(true);
  const [loadingTaskCompletion, setLoadingTaskCompletion] = useState(true); // New loading state for task completion

  const fetchData = async () => {
    setLoading(true);
    let hasError = false;

    // Fetch Incident Count
    const { count: incidents, error: incidentsError } = await supabase
      .from("incidents")
      .select("*", { count: "exact", head: true });
    if (incidentsError) {
      toast.error("Failed to load incident count: " + incidentsError.message);
      console.error("Error fetching incident count:", incidentsError);
      setIncidentCount(0);
      hasError = true;
    } else {
      setIncidentCount(incidents);
    }

    // Fetch Timesheet Count
    const { count: timesheets, error: timesheetsError } = await supabase
      .from("timesheets")
      .select("*", { count: "exact", head: true });
    if (timesheetsError) {
      toast.error("Failed to load timesheet count: " + timesheetsError.message);
      console.error("Error fetching timesheet count:", timesheetsError);
      setTimesheetCount(0);
      hasError = true;
    } else {
      setTimesheetCount(timesheets);
    }

    // Fetch Shift Count
    const { count: shifts, error: shiftsError } = await supabase
      .from("shifts")
      .select("*", { count: "exact", head: true });
    if (shiftsError) {
      toast.error("Failed to load shift count: " + shiftsError.message);
      console.error("Error fetching shift count:", shiftsError);
      setShiftCount(0);
      hasError = true;
    } else {
      setShiftCount(shifts);
    }

    // Fetch Incident Status Data for Chart
    const { data: incidentsByStatus, error: statusError } = await supabase
      .from("incidents")
      .select("status", { count: "exact" });

    if (statusError) {
      toast.error("Failed to load incident status data: " + statusError.message);
      console.error("Error fetching incident status data:", statusError);
      setIncidentStatusData([]);
      hasError = true;
    } else {
      const statusCounts: { [key: string]: number } = {};
      incidentsByStatus.forEach((incident: { status: string }) => {
        statusCounts[incident.status] = (statusCounts[incident.status] || 0) + 1;
      });
      const formattedData = Object.keys(statusCounts).map(status => ({
        name: status,
        value: statusCounts[status],
      }));
      setIncidentStatusData(formattedData);
    }

    setLoading(false);
    if (hasError) {
      toast.info("Some report data could not be loaded.");
    }
  };

  const fetchHoursWorkedData = async () => {
    setLoadingHoursWorked(true);
    const { data, error } = await supabase
      .from("timesheets")
      .select(`
        hours_worked,
        profiles (
          first_name,
          last_name
        )
      `);

    if (error) {
      toast.error("Failed to load hours worked data: " + error.message);
      console.error("Error fetching hours worked data:", error);
      setHoursWorkedData([]);
    } else {
      const aggregatedData: { [key: string]: number } = {};
      data.forEach((entry: any) => {
        const fullName = `${entry.profiles?.first_name || "Unknown"} ${entry.profiles?.last_name || ""}`;
        aggregatedData[fullName] = (aggregatedData[fullName] || 0) + entry.hours_worked;
      });
      const formattedData = Object.keys(aggregatedData).map(name => ({
        name,
        hours: aggregatedData[name],
      }));
      setHoursWorkedData(formattedData);
    }
    setLoadingHoursWorked(false);
  };

  const fetchTaskCompletionData = async () => {
    setLoadingTaskCompletion(true);
    const { data, error } = await supabase
      .from("tasks")
      .select(`
        status,
        user_id,
        profiles (
          first_name,
          last_name
        )
      `);

    if (error) {
      toast.error("Failed to load task completion data: " + error.message);
      console.error("Error fetching task completion data:", error);
      setTaskCompletionData([]);
    } else {
      const taskCounts: { [userId: string]: { total: number; completed: number; guardName: string } } = {};

      data.forEach((task: any) => {
        const userId = task.user_id;
        const guardName = `${task.profiles?.first_name || "Unknown"} ${task.profiles?.last_name || ""}`;

        if (!taskCounts[userId]) {
          taskCounts[userId] = { total: 0, completed: 0, guardName };
        }
        taskCounts[userId].total++;
        if (task.status === "Completed") {
          taskCounts[userId].completed++;
        }
      });

      const formattedData = Object.keys(taskCounts).map(userId => {
        const { total, completed, guardName } = taskCounts[userId];
        const completionRate = total > 0 ? ((completed / total) * 100).toFixed(1) + "%" : "0.0%";
        return { guardName, totalTasks: total, completedTasks: completed, completionRate };
      });
      setTaskCompletionData(formattedData);
    }
    setLoadingTaskCompletion(false);
  };

  useEffect(() => {
    fetchData();
    fetchHoursWorkedData();
    fetchTaskCompletionData(); // Fetch new data for task completion

    // Set up real-time subscriptions for relevant tables
    const incidentsChannel = supabase
      .channel('reports_incidents_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incidents' },
        () => {
          fetchData(); // Re-fetch all data on any incident change
        }
      )
      .subscribe();

    const timesheetsChannel = supabase
      .channel('reports_timesheets_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'timesheets' },
        () => {
          fetchData(); // Re-fetch all data on any timesheet change
          fetchHoursWorkedData(); // Re-fetch hours worked data on timesheet change
        }
      )
      .subscribe();

    const shiftsChannel = supabase
      .channel('reports_shifts_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'shifts' },
        () => {
          fetchData(); // Re-fetch all data on any shift change
        }
      )
      .subscribe();

    const tasksChannel = supabase
      .channel('reports_tasks_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'tasks' },
        () => {
          fetchTaskCompletionData(); // Re-fetch task completion data on task change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(incidentsChannel);
      supabase.removeChannel(timesheetsChannel);
      supabase.removeChannel(shiftsChannel);
      supabase.removeChannel(tasksChannel);
    };
  }, []);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Reports & Analytics</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <BarChart2 className="inline-block mr-2 h-5 w-5 text-orange-500" />
            Operational Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            This section provides an overview of key operational metrics.
          </p>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading report data...</p>
          ) : (
            <div className="mt-4 space-y-2">
              <div className="border rounded-md p-3 flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Total Incidents Reported</h4>
                  <p className="text-xs text-muted-foreground">All security incidents recorded.</p>
                </div>
                <span className="text-2xl font-bold text-red-600">{incidentCount}</span>
              </div>
              <div className="border rounded-md p-3 flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Total Timesheets Submitted</h4>
                  <p className="text-xs text-muted-foreground">All guard timesheet entries.</p>
                </div>
                <span className="text-2xl font-bold text-blue-600">{timesheetCount}</span>
              </div>
              <div className="border rounded-md p-3 flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Total Shifts Scheduled</h4>
                  <p className="text-xs text-muted-foreground">All scheduled guard shifts.</p>
                </div>
                <span className="text-2xl font-bold text-green-600">{shiftCount}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Incident Status Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading chart data...</p>
          ) : incidentStatusData.length > 0 ? (
            <IncidentStatusChart data={incidentStatusData} />
          ) : (
            <p className="text-sm text-muted-foreground">No incident data available for charting.</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Total Hours Worked by Guard
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loadingHoursWorked ? (
            <p className="text-sm text-muted-foreground">Loading hours worked data...</p>
          ) : hoursWorkedData.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={hoursWorkedData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="hours" fill="#8884d8" name="Hours Worked" />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <p className="text-sm text-muted-foreground">No timesheet data available for charting hours worked.</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold flex items-center">
            <ListChecks className="mr-2 h-5 w-5 text-green-500" />
            Task Completion Overview by Guard
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loadingTaskCompletion ? (
            <p className="text-sm text-muted-foreground">Loading task completion data...</p>
          ) : taskCompletionData.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                  <tr>
                    <th scope="col" className="px-6 py-3">Guard Name</th>
                    <th scope="col" className="px-6 py-3">Total Tasks</th>
                    <th scope="col" className="px-6 py-3">Completed Tasks</th>
                    <th scope="col" className="px-6 py-3">Completion Rate</th>
                  </tr>
                </thead>
                <tbody>
                  {taskCompletionData.map((data, index) => (
                    <tr key={index} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                      <td className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                        {data.guardName}
                      </td>
                      <td className="px-6 py-4">{data.totalTasks}</td>
                      <td className="px-6 py-4">{data.completedTasks}</td>
                      <td className="px-6 py-4">{data.completionRate}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No task data available for completion overview.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerReports;