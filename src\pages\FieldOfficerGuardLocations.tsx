import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { MapPin, User } from "lucide-react";
import { format } from "date-fns";
import GuardMap from "@/components/GuardMap"; // Import the new map component

interface GuardLocation {
  id: string;
  user_id: string;
  latitude: number;
  longitude: number;
  timestamp: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

const FieldOfficerGuardLocations = () => {
  const [guardLocations, setGuardLocations] = useState<GuardLocation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGuardLocations = async () => {
      setLoading(true);
      const { data, error } = await supabase
        .from("guard_locations")
        .select(`
          *,
          profiles (
            first_name,
            last_name
          )
        `)
        .order("timestamp", { ascending: false }); // Order by latest timestamp

      if (error) {
        toast.error("Failed to load guard locations: " + error.message);
        console.error("Error fetching guard locations:", error);
      } else {
        // Filter to get only the latest entry for each user_id
        const latestLocationsMap = new Map<string, GuardLocation>();
        (data as GuardLocation[]).forEach(location => {
          if (!latestLocationsMap.has(location.user_id) || new Date(location.timestamp) > new Date(latestLocationsMap.get(location.user_id)!.timestamp)) {
            latestLocationsMap.set(location.user_id, location);
          }
        });
        setGuardLocations(Array.from(latestLocationsMap.values()));
      }
      setLoading(false);
    };

    fetchGuardLocations();

    // Set up real-time subscription for guard locations
    const channel = supabase
      .channel('guard_locations_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'guard_locations' },
        (payload) => {
          console.log('Change received!', payload);
          fetchGuardLocations(); // Re-fetch data on any change
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Live Guard Locations</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <MapPin className="inline-block mr-2 h-5 w-5 text-blue-500" />
            Current Guard Positions
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading guard locations...</p>
          ) : guardLocations.length === 0 ? (
            <p className="text-sm text-muted-foreground">No active guard locations to display.</p>
          ) : (
            <div className="space-y-6">
              <GuardMap locations={guardLocations} /> {/* Integrated Map Component */}
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Guard</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead>Latitude</TableHead>
                      <TableHead>Longitude</TableHead>
                      <TableHead className="text-right">View on Map</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {guardLocations.map((location) => (
                      <TableRow key={location.id}>
                        <TableCell className="font-medium">
                          {location.profiles?.first_name || "N/A"} {location.profiles?.last_name || ""}
                        </TableCell>
                        <TableCell>
                          {format(new Date(location.timestamp), "PPpp")}
                        </TableCell>
                        <TableCell>{location.latitude.toFixed(6)}</TableCell>
                        <TableCell>{location.longitude.toFixed(6)}</TableCell>
                        <TableCell className="text-right">
                          <a
                            href={`https://www.google.com/maps/search/?api=1&query=${location.latitude},${location.longitude}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline flex items-center justify-end"
                          >
                            <MapPin className="h-4 w-4 mr-1" /> Map
                          </a>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerGuardLocations;