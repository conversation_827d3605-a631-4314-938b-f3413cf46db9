import React, { useEffect, useState } from "react";
import { Card, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { Clock, Edit, Trash, CalendarIcon, CheckCircle2, MessageSquare } from "lucide-react"; // Added MessageSquare icon
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/context/AuthContext"; // Import useAuth to get current user for comments

interface Timesheet {
  id: string;
  user_id: string;
  shift_date: string;
  hours_worked: number;
  description: string | null;
  created_at: string;
  status: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
  timesheet_comments: TimesheetComment[]; // Added comments to the interface
}

interface TimesheetComment {
  id: string;
  timesheet_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

const FieldOfficerTimesheets = () => {
  const { user: currentUser } = useAuth(); // Get current logged-in user
  const [timesheets, setTimesheets] = useState<Timesheet[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [currentTimesheet, setCurrentTimesheet] = useState<Timesheet | null>(null);
  const [timesheetComments, setTimesheetComments] = useState<TimesheetComment[]>([]); // State for comments

  // State for edit form
  const [editShiftDate, setEditShiftDate] = useState<Date | undefined>(new Date());
  const [editHoursWorked, setEditHoursWorked] = useState("");
  const [editDescription, setEditDescription] = useState("");
  const [editStatus, setEditStatus] = useState("");
  const [newCommentText, setNewCommentText] = useState(""); // New state for new comment
  const [commentLoading, setCommentLoading] = useState(false); // Loading state for comment submission

  const fetchTimesheets = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from("timesheets")
      .select(`
          *,
          profiles (
            first_name,
            last_name
          ),
          timesheet_comments (
            id,
            user_id,
            comment_text,
            created_at,
            profiles (
              first_name,
              last_name,
              role
            )
          )
        `)
      .order("shift_date", { ascending: false });

    if (error) {
      toast.error("Failed to load timesheets: " + error.message);
      console.error("Error fetching timesheets:", error);
    } else {
      // Sort comments within each timesheet by created_at
      const timesheetsWithSortedComments = (data as Timesheet[]).map(timesheet => ({
        ...timesheet,
        timesheet_comments: timesheet.timesheet_comments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
      }));
      setTimesheets(timesheetsWithSortedComments);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchTimesheets();

    // Set up real-time subscription for timesheets and comments
    const timesheetsChannel = supabase
      .channel('timesheets_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'timesheets' },
        (payload) => {
          console.log('Timesheet change received!', payload);
          fetchTimesheets(); // Re-fetch data on any change
        }
      )
      .subscribe();

    const commentsChannel = supabase
      .channel('timesheet_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'timesheet_comments' },
        (payload) => {
          console.log('Timesheet comment change received!', payload);
          if (currentTimesheet && payload.new.timesheet_id === currentTimesheet.id) {
            fetchTimesheetComments(currentTimesheet.id); // Re-fetch comments for the open dialog
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(timesheetsChannel);
      supabase.removeChannel(commentsChannel);
    };
  }, [currentTimesheet]); // Re-run effect if currentTimesheet changes to update comment subscription

  const fetchTimesheetComments = async (timesheetId: string) => {
    const { data, error } = await supabase
      .from("timesheet_comments")
      .select(`
          *,
          profiles (
            first_name,
            last_name,
            role
          )
        `)
      .eq("timesheet_id", timesheetId)
      .order("created_at", { ascending: true });

    if (error) {
      console.error("Error fetching timesheet comments:", error.message);
      setTimesheetComments([]);
    } else {
      setTimesheetComments(data as TimesheetComment[]);
    }
  };

  const handleEditClick = (timesheet: Timesheet) => {
    setCurrentTimesheet(timesheet);
    setEditShiftDate(new Date(timesheet.shift_date));
    setEditHoursWorked(timesheet.hours_worked.toString());
    setEditDescription(timesheet.description || "");
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (timesheet: Timesheet) => {
    setCurrentTimesheet(timesheet);
    setIsDeleteDialogOpen(true);
  };

  const handleStatusClick = (timesheet: Timesheet) => {
    setCurrentTimesheet(timesheet);
    setEditStatus(timesheet.status); // Set current status for the dialog
    setNewCommentText(""); // Clear new comment input
    fetchTimesheetComments(timesheet.id); // Fetch comments when dialog opens
    setIsStatusDialogOpen(true);
  };

  const handleUpdateTimesheet = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentTimesheet) return;

    if (!editShiftDate || !editHoursWorked) {
      toast.error("Please fill in all required fields (Date and Hours Worked).");
      return;
    }
    const parsedHours = parseFloat(editHoursWorked);
    if (isNaN(parsedHours) || parsedHours <= 0) {
      toast.error("Please enter a valid number for hours worked.");
      return;
    }

    setLoading(true);
    const { error } = await supabase
      .from("timesheets")
      .update({
        shift_date: format(editShiftDate, "yyyy-MM-dd"),
        hours_worked: parsedHours,
        description: editDescription || null,
      })
      .eq("id", currentTimesheet.id);

    if (error) {
      toast.error("Failed to update timesheet: " + error.message);
      console.error("Timesheet update error:", error);
    } else {
      toast.success("Timesheet updated successfully!");
      setIsEditDialogOpen(false);
      fetchTimesheets(); // Re-fetch timesheets to show updated data
    }
    setLoading(false);
  };

  const handleUpdateStatus = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentTimesheet) return;

    setLoading(true);
    const { error } = await supabase
      .from("timesheets")
      .update({ status: editStatus })
      .eq("id", currentTimesheet.id);

    if (error) {
      toast.error("Failed to update timesheet status: " + error.message);
      console.error("Timesheet status update error:", error);
    } else {
      toast.success("Timesheet status updated successfully!");
      setIsStatusDialogOpen(false);
      fetchTimesheets(); // Re-fetch timesheets to show updated data
    }
    setLoading(false);
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentTimesheet || !newCommentText.trim()) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setCommentLoading(true);
    if (!currentUser) {
      toast.error("You must be logged in to add a comment.");
      setCommentLoading(false);
      return;
    }

    const { error } = await supabase.from("timesheet_comments").insert({
      timesheet_id: currentTimesheet.id,
      user_id: currentUser.id,
      comment_text: newCommentText.trim(),
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewCommentText("");
      fetchTimesheetComments(currentTimesheet.id); // Re-fetch comments to show the new one
    }
    setCommentLoading(false);
  };

  const confirmDeleteTimesheet = async () => {
    if (!currentTimesheet) return;

    setLoading(true);
    const { error } = await supabase
      .from("timesheets")
      .delete()
      .eq("id", currentTimesheet.id);

    if (error) {
      toast.error("Failed to delete timesheet: " + error.message);
      console.error("Timesheet deletion error:", error);
    } else {
      toast.success("Timesheet deleted successfully!");
      setIsDeleteDialogOpen(false);
      fetchTimesheets(); // Re-fetch timesheets to show updated data
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Timesheet Overview</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <Clock className="inline-block mr-2 h-5 w-5 text-blue-500" />
            All Submitted Timesheets
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading timesheets...</p>
          ) : timesheets.length === 0 ? (
            <p className="text-sm text-muted-foreground">No timesheets submitted yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Guard</TableHead>
                    <TableHead>Hours</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Status</TableHead> {/* New TableHead */}
                    <TableHead>Submitted On</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {timesheets.map((timesheet) => (
                    <TableRow key={timesheet.id}>
                      <TableCell className="font-medium">
                        {format(new Date(timesheet.shift_date), "PPP")}
                      </TableCell>
                      <TableCell>
                        {timesheet.profiles?.first_name || "N/A"} {timesheet.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell>{timesheet.hours_worked}</TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {timesheet.description || "No description"}
                      </TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          timesheet.status === 'Pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                          timesheet.status === 'Approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {timesheet.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        {format(new Date(timesheet.created_at), "PPpp")}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(timesheet)} className="mr-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleStatusClick(timesheet)} className="mr-2">
                          <CheckCircle2 className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(timesheet)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Timesheet Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Timesheet Entry</DialogTitle>
            <DialogDescription>
              Make changes to the timesheet entry here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateTimesheet} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="editShiftDate">Shift Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !editShiftDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {editShiftDate ? format(editShiftDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={editShiftDate}
                    onSelect={setEditShiftDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editHoursWorked">Hours Worked</Label>
              <Input
                id="editHoursWorked"
                type="number"
                step="0.01"
                placeholder="e.g., 8.5"
                required
                value={editHoursWorked}
                onChange={(e) => setEditHoursWorked(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editDescription">Description (Optional)</Label>
              <Textarea
                id="editDescription"
                placeholder="e.g., Regular shift, covered for John Doe"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
              />
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Update Status Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Update Timesheet Status</DialogTitle>
            <DialogDescription>
              Update the status for the timesheet submitted by {currentTimesheet?.profiles?.first_name} {currentTimesheet?.profiles?.last_name} for {currentTimesheet?.shift_date ? format(new Date(currentTimesheet.shift_date), "PPP") : "N/A"}.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateStatus} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="currentHours">Hours Worked</Label>
              <p id="currentHours" className="text-sm text-muted-foreground">
                {currentTimesheet?.hours_worked} hours
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="currentDescription">Description</Label>
              <p id="currentDescription" className="text-sm text-muted-foreground">
                {currentTimesheet?.description || "No description provided."}
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editStatus">Status</Label>
              <Select onValueChange={setEditStatus} value={editStatus}>
                <SelectTrigger id="editStatus">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="Approved">Approved</SelectItem>
                  <SelectItem value="Rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>

          <div className="mt-6 pt-4 border-t">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              Comments
            </h3>
            <div className="space-y-3 max-h-48 overflow-y-auto pr-2">
              {timesheetComments.length === 0 ? (
                <p className="text-sm text-muted-foreground">No comments yet.</p>
              ) : (
                timesheetComments.map((comment) => (
                  <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                    <p className="text-sm font-medium">
                      {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                      <span className="text-xs text-muted-foreground ml-2">
                        ({comment.profiles?.role || "N/A"})
                      </span>
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(comment.created_at), "PPpp")}
                    </p>
                    <p className="text-sm mt-1">{comment.comment_text}</p>
                  </div>
                ))
              )}
            </div>
            <form onSubmit={handleAddComment} className="mt-4 flex gap-2">
              <Textarea
                placeholder="Add a new comment..."
                value={newCommentText}
                onChange={(e) => setNewCommentText(e.target.value)}
                rows={2}
                className="flex-1"
                disabled={commentLoading}
              />
              <Button type="submit" disabled={commentLoading}>
                {commentLoading ? "Adding..." : "Add Comment"}
              </Button>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Timesheet Alert Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the timesheet entry for{" "}
              <span className="font-semibold">
                {currentTimesheet ? format(new Date(currentTimesheet.shift_date), "PPP") : ""}
              </span>{" "}
              by{" "}
              <span className="font-semibold">
                {currentTimesheet?.profiles?.first_name || "N/A"} {currentTimesheet?.profiles?.last_name || ""}
              </span>.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteTimesheet} disabled={loading}>
              {loading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default FieldOfficerTimesheets;