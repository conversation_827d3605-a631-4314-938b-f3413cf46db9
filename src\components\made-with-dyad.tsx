import { useOrganization } from "@/hooks/useOrganization"; // Import useOrganization hook

export const MadeWithDyad = () => {
  const { hasBranding, loading } = useOrganization();

  if (loading) {
    return null; // Or a loading spinner if preferred for this small component
  }

  // If hasBranding is true, it means the plan includes white-labeling, so we return null (no branding).
  // If hasBranding is false, it means branding is present, so we display "Powered by MirazSec".
  if (hasBranding) {
    return null;
  }

  return (
    <div className="p-4 text-center">
      <a
        href="https://www.dyad.sh/"
        target="_blank"
        rel="noopener noreferrer"
        className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
      >
        Powered by MirazSec
      </a>
    </div>
  );
};