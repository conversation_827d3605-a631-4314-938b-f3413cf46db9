import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DollarSign, Banknote } from "lucide-react";

interface GuardProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
}

const FieldOfficerInitiatePayout = () => {
  const [guards, setGuards] = useState<GuardProfile[]>([]);
  const [selectedGuardId, setSelectedGuardId] = useState<string>("");
  const [amount, setAmount] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchGuards = async () => {
      const { data, error } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .eq("role", "guard");

      if (error) {
        toast.error("Failed to load guards: " + error.message);
        console.error("Error fetching guards:", error);
      } else {
        setGuards(data || []);
      }
    };
    fetchGuards();
  }, []);

  const handleInitiatePayout = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!selectedGuardId || !amount || parseFloat(amount) <= 0) {
      toast.error("Please select a guard and enter a valid amount.");
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase.functions.invoke('initiate-payout', {
        body: {
          userId: selectedGuardId,
          amount: parseFloat(amount),
        },
      });

      if (error) {
        toast.error("Payout failed: " + error.message);
        console.error("Payout initiation error:", error);
      } else {
        toast.success("Payout initiated successfully!");
        setSelectedGuardId("");
        setAmount("");
      }
    } catch (error: any) {
      toast.error("An unexpected error occurred: " + error.message);
      console.error("Unexpected payout error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Initiate Guard Payout</h1>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Banknote className="mr-2 h-5 w-5 text-green-500" />
            Send Salary / Payment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleInitiatePayout} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="guard">Select Guard</Label>
              <Select onValueChange={setSelectedGuardId} value={selectedGuardId}>
                <SelectTrigger id="guard">
                  <SelectValue placeholder="Select a guard" />
                </SelectTrigger>
                <SelectContent>
                  {guards.map((guard) => (
                    <SelectItem key={guard.id} value={guard.id}>
                      {guard.first_name} {guard.last_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="amount">Amount (INR)</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                placeholder="e.g., 15000.00"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Initiating Payout..." : "Initiate Payout"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default FieldOfficerInitiatePayout;