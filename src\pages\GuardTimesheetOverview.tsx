import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/context/AuthContext";
import { Clock, MessageSquare } from "lucide-react"; // Added MessageSquare icon
import { format } from "date-fns";
import { Textarea } from "@/components/ui/textarea"; // Import Textarea
import { Button } from "@/components/ui/button"; // Import Button

interface Timesheet {
  id: string;
  user_id: string;
  shift_date: string;
  hours_worked: number;
  description: string | null;
  created_at: string;
  status: string; // Added status to Timesheet interface
  timesheet_comments: TimesheetComment[]; // Added comments to the interface
}

interface TimesheetComment {
  id: string;
  timesheet_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

const GuardTimesheetOverview = () => {
  const { user, loading: authLoading } = useAuth();
  const [timesheets, setTimesheets] = useState<Timesheet[]>([]);
  const [loading, setLoading] = useState(true);
  const [newCommentText, setNewCommentText] = useState<{ [key: string]: string }>({});
  const [commentLoading, setCommentLoading] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const fetchTimesheets = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      setLoading(true);
      const { data, error } = await supabase
        .from("timesheets")
        .select(`
          *,
          timesheet_comments (
            id,
            user_id,
            comment_text,
            created_at,
            profiles (
              first_name,
              last_name,
              role
            )
          )
        `)
        .eq("user_id", user.id)
        .order("created_at", { ascending: false }); // Order by latest timesheet submission

      if (error) {
        toast.error("Failed to load your timesheets: " + error.message);
        console.error("Error fetching guard timesheets:", error);
        setTimesheets([]);
      } else {
        // Sort comments within each timesheet by created_at
        const timesheetsWithSortedComments = (data as Timesheet[]).map(timesheet => ({
          ...timesheet,
          timesheet_comments: timesheet.timesheet_comments.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        }));
        setTimesheets(timesheetsWithSortedComments);
      }
      setLoading(false);
    };

    if (!authLoading) {
      fetchTimesheets();
    }

    // Set up real-time subscription for timesheets and comments
    const timesheetsChannel = supabase
      .channel('guard_timesheet_overview_timesheets_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'timesheets' },
        () => {
          fetchTimesheets();
        }
      )
      .subscribe();

    const commentsChannel = supabase
      .channel('guard_timesheet_overview_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'timesheet_comments' },
        () => {
          fetchTimesheets(); // Re-fetch timesheets to update comments
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(timesheetsChannel);
      supabase.removeChannel(commentsChannel);
    };
  }, [user, authLoading]);

  const handleAddComment = async (timesheetId: string) => {
    const commentText = newCommentText[timesheetId]?.trim();
    if (!user) {
      toast.error("You must be logged in to add a comment.");
      return;
    }
    if (!commentText) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setCommentLoading(prev => ({ ...prev, [timesheetId]: true }));
    const { error } = await supabase.from("timesheet_comments").insert({
      timesheet_id: timesheetId,
      user_id: user.id,
      comment_text: commentText,
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Timesheet comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewCommentText(prev => ({ ...prev, [timesheetId]: "" }));
    }
    setCommentLoading(prev => ({ ...prev, [timesheetId]: false }));
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">My Timesheets</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <Clock className="inline-block mr-2 h-5 w-5 text-blue-500" />
            My Submitted Timesheet Entries
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Loading timesheets...</p>
          ) : timesheets.length === 0 ? (
            <p className="text-sm text-muted-foreground">You have not submitted any timesheets yet.</p>
          ) : (
            <div className="grid gap-4"> {/* Changed to grid for better spacing of cards */}
              {timesheets.map((timesheet) => (
                <Card key={timesheet.id} className="p-4"> {/* Each timesheet as a Card */}
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-lg">
                      Shift Date: {format(new Date(timesheet.shift_date), "PPP")}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${
                      timesheet.status === 'Pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' :
                      timesheet.status === 'Approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {timesheet.status}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">Hours Worked: {timesheet.hours_worked}</p>
                  <p className="text-sm text-muted-foreground">Description: {timesheet.description || "No description"}</p>
                  <p className="text-sm text-muted-foreground">Submitted On: {format(new Date(timesheet.created_at), "PPpp")}</p>

                  {/* Timesheet Comments Section */}
                  {timesheet.timesheet_comments && timesheet.timesheet_comments.length > 0 && (
                    <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <h4 className="text-md font-semibold mb-2 flex items-center">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Comments
                      </h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
                        {timesheet.timesheet_comments.map((comment) => (
                          <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
                            <p className="text-xs font-medium">
                              {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({comment.profiles?.role || "N/A"})
                              </span>
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {format(new Date(comment.created_at), "PPpp")}
                            </p>
                            <p className="text-sm mt-1">{comment.comment_text}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  <form onSubmit={(e) => { e.preventDefault(); handleAddComment(timesheet.id); }} className="mt-4 flex gap-2">
                    <Textarea
                      placeholder="Add a new comment..."
                      value={newCommentText[timesheet.id] || ""}
                      onChange={(e) => setNewCommentText(prev => ({ ...prev, [timesheet.id]: e.target.value }))}
                      rows={2}
                      className="flex-1"
                      disabled={commentLoading[timesheet.id]}
                    />
                    <Button type="submit" disabled={commentLoading[timesheet.id]}>
                      {commentLoading[timesheet.id] ? "Adding..." : "Add Comment"}
                    </Button>
                  </form>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GuardTimesheetOverview;