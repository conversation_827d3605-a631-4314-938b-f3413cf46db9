import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/lib/supabaseClient";
import { AlertTriangle, Edit, Trash, MessageSquare, CalendarIcon, ArrowUp, ArrowDown } from "lucide-react"; // Added icons for sorting
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/context/AuthContext"; // Import useAuth to get current user for comments

interface Incident {
  id: string;
  user_id: string;
  title: string;
  description: string;
  location: string;
  image_url: string | null;
  created_at: string;
  status: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
  } | null;
}

interface IncidentComment {
  id: string;
  incident_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
  profiles: {
    first_name: string | null;
    last_name: string | null;
    role: string | null;
  } | null;
}

interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
}

const FieldOfficerIncidentOverview = () => {
  const { user: currentUser } = useAuth(); // Get current logged-in user
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [users, setUsers] = useState<UserProfile[]>([]); // State for users to filter by
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentIncident, setCurrentIncident] = useState<Incident | null>(null);
  const [incidentComments, setIncidentComments] = useState<IncidentComment[]>([]);

  // State for edit form
  const [editTitle, setEditTitle] = useState("");
  const [editDescription, setEditDescription] = useState("");
  const [editLocation, setEditLocation] = useState("");
  const [editStatus, setEditStatus] = useState("");
  const [editImageFile, setEditImageFile] = useState<File | null>(null);
  const [editImageUrl, setEditImageUrl] = useState<string | null>(null);
  const [newCommentText, setNewCommentText] = useState("");
  const [commentLoading, setCommentLoading] = useState(false); // Loading state for comment submission

  // State for filters and sorting
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterReportedBy, setFilterReportedBy] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const fetchIncidents = async () => {
    setLoading(true);
    let query = supabase
      .from("incidents")
      .select(`
          *,
          profiles (
            first_name,
            last_name
          )
        `);

    // Apply filters
    if (filterStatus !== "all") {
      query = query.eq("status", filterStatus);
    }
    if (filterReportedBy !== "all") {
      query = query.eq("user_id", filterReportedBy);
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === "asc" });

    const { data, error } = await query;

    if (error) {
      toast.error("Failed to load incidents: " + error.message);
      console.error("Error fetching incidents:", error);
    } else {
      setIncidents(data as Incident[]);
    }
    setLoading(false);
  };

  const fetchIncidentComments = async (incidentId: string) => {
    const { data, error } = await supabase
      .from("incident_comments")
      .select(`
          *,
          profiles (
            first_name,
            last_name,
            role
          )
        `)
      .eq("incident_id", incidentId)
      .order("created_at", { ascending: true });

    if (error) {
      console.error("Error fetching incident comments:", error.message);
      setIncidentComments([]);
    } else {
      setIncidentComments(data as IncidentComment[]);
    }
  };

  const fetchUsers = async () => {
    const { data, error } = await supabase
      .from("profiles")
      .select("id, first_name, last_name, role");

    if (error) {
      toast.error("Failed to load users for filtering: " + error.message);
      console.error("Error fetching users:", error);
    } else {
      setUsers(data || []);
    }
  };

  useEffect(() => {
    fetchIncidents();
  }, [filterStatus, filterReportedBy, sortBy, sortOrder]); // Re-fetch on filter/sort change

  useEffect(() => {
    fetchUsers();

    // Set up real-time subscription for incidents
    const incidentsChannel = supabase
      .channel('incidents_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incidents' },
        (payload) => {
          console.log('Incident change received!', payload);
          fetchIncidents(); // Re-fetch data on any change
        }
      )
      .subscribe();

    // Set up real-time subscription for incident comments
    const commentsChannel = supabase
      .channel('incident_comments_changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'incident_comments' },
        (payload) => {
          console.log('Incident comment change received!', payload);
          if (currentIncident && payload.new.incident_id === currentIncident.id) {
            fetchIncidentComments(currentIncident.id); // Re-fetch comments for the open dialog
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(incidentsChannel);
      supabase.removeChannel(commentsChannel);
    };
  }, [currentIncident]); // Re-run effect if currentIncident changes to update comment subscription

  const handleEditClick = (incident: Incident) => {
    setCurrentIncident(incident);
    setEditTitle(incident.title);
    setEditDescription(incident.description);
    setEditLocation(incident.location);
    setEditStatus(incident.status);
    setEditImageUrl(incident.image_url);
    setEditImageFile(null);
    setNewCommentText(""); // Clear new comment input
    fetchIncidentComments(incident.id); // Fetch comments when dialog opens
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (incident: Incident) => {
    setCurrentIncident(incident);
    setIsDeleteDialogOpen(true);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setEditImageFile(e.target.files[0]);
      setEditImageUrl(URL.createObjectURL(e.target.files[0]));
    } else {
      setEditImageFile(null);
      setEditImageUrl(currentIncident?.image_url || null);
    }
  };

  const handleUpdateIncident = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentIncident) return;

    if (!editTitle || !editDescription || !editLocation || !editStatus) {
      toast.error("Please fill in all required fields for the incident.");
      return;
    }

    setLoading(true);
    let newImageUrl = currentIncident.image_url;

    if (editImageFile) {
      const fileExt = editImageFile.name.split(".").pop();
      const fileName = `${currentIncident.user_id}/${Date.now()}.${fileExt}`;
      const filePath = `incidents/${fileName}`;

      if (currentIncident.image_url) {
        const oldPath = currentIncident.image_url.split("incident_media/")[1];
        if (oldPath) {
          const { error: deleteOldError } = await supabase.storage
            .from("incident_media")
            .remove([oldPath]);
          if (deleteOldError) {
            console.error("Failed to delete old image:", deleteOldError.message);
          }
        }
      }

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("incident_media")
        .upload(filePath, editImageFile, {
          cacheControl: "3600",
          upsert: false,
        });

      if (uploadError) {
        toast.error("Failed to upload new image: " + uploadError.message);
        setLoading(false);
        return;
      }

      const { data: publicUrlData } = supabase.storage
        .from("incident_media")
        .getPublicUrl(filePath);
      
      newImageUrl = publicUrlData.publicUrl;
    } else if (editImageUrl === null && currentIncident.image_url !== null) {
      const oldPath = currentIncident.image_url.split("incident_media/")[1];
      if (oldPath) {
        const { error: deleteOldError } = await supabase.storage
          .from("incident_media")
          .remove([oldPath]);
        if (deleteOldError) {
          console.error("Failed to delete old image:", deleteOldError.message);
        }
      }
      newImageUrl = null;
    }

    const { error } = await supabase
      .from("incidents")
      .update({
        title: editTitle,
        description: editDescription,
        location: editLocation,
        image_url: newImageUrl,
        status: editStatus,
      })
      .eq("id", currentIncident.id);

    if (error) {
      toast.error("Failed to update incident: " + error.message);
      console.error("Incident update error:", error);
    } else {
      toast.success("Incident updated successfully!");
      setIsEditDialogOpen(false);
      fetchIncidents();
    }
    setLoading(false);
  };

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentIncident || !newCommentText.trim()) {
      toast.error("Comment cannot be empty.");
      return;
    }

    setCommentLoading(true);
    if (!currentUser) {
      toast.error("You must be logged in to add a comment.");
      setCommentLoading(false);
      return;
    }

    const { error } = await supabase.from("incident_comments").insert({
      incident_id: currentIncident.id,
      user_id: currentUser.id,
      comment_text: newCommentText.trim(),
    });

    if (error) {
      toast.error("Failed to add comment: " + error.message);
      console.error("Comment submission error:", error);
    } else {
      toast.success("Comment added successfully!");
      setNewCommentText("");
      fetchIncidentComments(currentIncident.id); // Re-fetch comments to show the new one
    }
    setCommentLoading(false);
  };

  const confirmDeleteIncident = async () => {
    if (!currentIncident) return;

    setLoading(true);

    if (currentIncident.image_url) {
      const pathSegments = currentIncident.image_url.split("incident_media/");
      if (pathSegments.length > 1) {
        const filePathInStorage = pathSegments[1];
        const { error: storageError } = await supabase.storage
          .from("incident_media")
          .remove([filePathInStorage]);

        if (storageError) {
          toast.error("Failed to delete incident image from storage: " + storageError.message);
          console.error("Storage deletion error:", storageError);
          setLoading(false);
          return;
        }
      }
    }

    const { error } = await supabase
      .from("incidents")
      .delete()
      .eq("id", currentIncident.id);

    if (error) {
      toast.error("Failed to delete incident: " + error.message);
      console.error("Incident deletion error:", error);
    } else {
      toast.success("Incident deleted successfully!");
      setIsDeleteDialogOpen(false);
      fetchIncidents();
    }
    setLoading(false);
  };

  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">Incident Overview</h1>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-xl font-semibold">
            <AlertTriangle className="inline-block mr-2 h-5 w-5 text-red-500" />
            All Reported Incidents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="filterStatus">Filter by Status</Label>
              <Select onValueChange={setFilterStatus} value={filterStatus}>
                <SelectTrigger id="filterStatus">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Open">Open</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Resolved">Resolved</SelectItem>
                  <SelectItem value="Closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="filterReportedBy">Filter by Reporter</Label>
              <Select onValueChange={setFilterReportedBy} value={filterReportedBy}>
                <SelectTrigger id="filterReportedBy">
                  <SelectValue placeholder="All Reporters" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Reporters</SelectItem>
                  {users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.first_name} {user.last_name} ({user.role})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[150px]">
              <Label htmlFor="sortBy">Sort By</Label>
              <Select onValueChange={setSortBy} value={sortBy}>
                <SelectTrigger id="sortBy">
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Created At</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                  <SelectItem value="location">Location</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 min-w-[100px]">
              <Label htmlFor="sortOrder">Order</Label>
              <Select onValueChange={setSortOrder} value={sortOrder}>
                <SelectTrigger id="sortOrder">
                  <SelectValue placeholder="Order" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {loading ? (
            <p className="text-sm text-muted-foreground">Loading incidents...</p>
          ) : incidents.length === 0 ? (
            <p className="text-sm text-muted-foreground">No incidents reported yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Reported By</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Image</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {incidents.map((incident) => (
                    <TableRow key={incident.id}>
                      <TableCell className="font-medium">
                        {format(new Date(incident.created_at), "PPpp")}
                      </TableCell>
                      <TableCell>{incident.title}</TableCell>
                      <TableCell>
                        {incident.profiles?.first_name || "N/A"} {incident.profiles?.last_name || ""}
                      </TableCell>
                      <TableCell>{incident.location}</TableCell>
                      <TableCell className="capitalize">
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                          incident.status === 'Open' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          incident.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          incident.status === 'Resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}>
                          {incident.status}
                        </span>
                      </TableCell>
                      <TableCell className="max-w-[300px] truncate">
                        {incident.description}
                      </TableCell>
                      <TableCell>
                        {incident.image_url ? (
                          <a href={incident.image_url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                            View Image
                          </a>
                        ) : (
                          "N/A"
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(incident)} className="mr-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteClick(incident)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Incident Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Incident</DialogTitle>
            <DialogDescription>
              Make changes to the incident report and view/add comments.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateIncident} className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="editTitle">Incident Title</Label>
              <Input
                id="editTitle"
                placeholder="e.g., Suspicious Activity"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editDescription">Description</Label>
              <Textarea
                id="editDescription"
                placeholder="Provide a detailed description..."
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                required
                rows={4}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editLocation">Location</Label>
              <Input
                id="editLocation"
                placeholder="e.g., Main Gate, Sector C"
                value={editLocation}
                onChange={(e) => setEditLocation(e.target.value)}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editStatus">Status</Label>
              <Select onValueChange={setEditStatus} value={editStatus}>
                <SelectTrigger id="editStatus">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Open">Open</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Resolved">Resolved</SelectItem>
                  <SelectItem value="Closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="editIncidentImage">Attach New Image (Optional)</Label>
              <Input
                id="editIncidentImage"
                type="file"
                accept="image/*"
                onChange={handleFileChange}
              />
              {editImageUrl && (
                <div className="mt-2">
                  <p className="text-sm text-muted-foreground mb-1">Current Image:</p>
                  <img src={editImageUrl} alt="Incident Preview" className="max-w-full h-auto rounded-md" />
                  <Button variant="ghost" size="sm" onClick={() => setEditImageUrl(null)} className="mt-2 text-red-500 hover:text-red-600">
                    Remove Current Image
                  </Button>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : "Save changes"}
              </Button>
            </DialogFooter>
          </form>

          <div className="mt-6 pt-4 border-t">
            <h3 className="text-lg font-semibold mb-3 flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              Comments
            </h3>
            <div className="space-y-3 max-h-48 overflow-y-auto pr-2">
              {incidentComments.length === 0 ? (
                <p className="text-sm text-muted-foreground">No comments yet.</p>
              ) : (
                incidentComments.map((comment) => (
                  <div key={comment.id} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                    <p className="text-sm font-medium">
                      {comment.profiles?.first_name || "Unknown"} {comment.profiles?.last_name || ""}
                      <span className="text-xs text-muted-foreground ml-2">
                        ({comment.profiles?.role || "N/A"})
                      </span>
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {format(new Date(comment.created_at), "PPpp")}
                    </p>
                    <p className="text-sm mt-1">{comment.comment_text}</p>
                  </div>
                ))
              )}
            </div>
            <form onSubmit={handleAddComment} className="mt-4 flex gap-2">
              <Textarea
                placeholder="Add a new comment..."
                value={newCommentText}
                onChange={(e) => setNewCommentText(e.target.value)}
                rows={2}
                className="flex-1"
                disabled={commentLoading}
              />
              <Button type="submit" disabled={commentLoading}>
                {commentLoading ? "Adding..." : "Add Comment"}
              </Button>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Incident Alert Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the incident report titled{" "}
              <span className="font-semibold">
                "{currentIncident ? currentIncident.title : ""}"
              </span>{" "}
              and remove any associated image.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteIncident} disabled={loading}>
              {loading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default FieldOfficerIncidentOverview;